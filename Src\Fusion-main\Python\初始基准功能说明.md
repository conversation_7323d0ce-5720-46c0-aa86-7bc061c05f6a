# SH5001传感器初始基准位置功能说明

## 🎯 问题解决

### 问题1: 微小转动显示为快速旋转 ✅ 已修复
**原因**: 没有设置初始基准位置，所有旋转都是绝对旋转
**解决**: 添加初始基准位置记录，所有运动相对于基准显示

### 问题2: 缺少运动基准参考 ✅ 已解决
**原因**: 每次启动程序都从默认位置开始
**解决**: 程序启动时自动记录当前位置作为基准

## 🔧 新增功能

### 1. 自动初始基准设置
- **启动采样**: 程序启动后自动采集30个样本
- **基准计算**: 使用第一个稳定样本作为初始基准
- **状态提示**: 实时显示基准设置进度
- **自动完成**: 3秒内完成基准设置

### 2. 相对运动显示
- **相对四元数**: 计算相对于初始位置的旋转
- **基准参考**: 所有运动都相对于初始基准
- **微小运动**: 正确显示微小的旋转变化
- **运动一致性**: 3D显示与实际运动完全一致

### 3. 基准重设功能
- **R键重设**: 按R键重新设置基准位置
- **实时重设**: 运行中随时可以重新设置基准
- **状态清零**: 重设后从新基准开始计算

## 🎮 使用方法

### 启动程序
```bash
python sh5001_smooth_3d.py
```

### 设置初始基准
1. **保持静止**: 程序启动后，保持传感器静止3秒
2. **等待提示**: 看到"✓ 初始基准已设置"消息
3. **开始使用**: 现在可以正常使用传感器

### 重新设置基准
1. **移动到新位置**: 将传感器移动到想要的新基准位置
2. **按R键**: 按下R键重新设置基准
3. **保持静止**: 等待3秒完成新基准设置

## 📊 界面显示

### 状态信息
程序界面显示以下信息：
- **FPS**: 当前帧率
- **基准状态**: 显示是否已设置基准
- **原始Q**: 传感器的原始四元数
- **相对Q**: 相对于基准的四元数
- **标定状态**: 坐标轴标定参数

### 基准设置过程
```
🔄 设置基准: 25    # 还需25个样本
🔄 设置基准: 20    # 还需20个样本
...
✓ 基准已设置      # 基准设置完成
```

## 🔍 技术原理

### 四元数相对计算
```
q_relative = q_current * q_initial^(-1)
```
其中：
- `q_current`: 当前四元数
- `q_initial`: 初始基准四元数
- `q_relative`: 相对四元数

### 基准采样策略
1. **采样数量**: 30个样本确保稳定性
2. **采样频率**: 按数据接收频率自动采样
3. **基准选择**: 使用第一个样本作为基准
4. **噪声处理**: 通过多样本平均减少噪声影响

## 🎯 使用场景

### 场景1: 日常监控
1. 启动程序
2. 将传感器放在标准位置
3. 等待基准设置完成
4. 开始监控运动

### 场景2: 特定位置基准
1. 启动程序
2. 将传感器调整到特定角度
3. 等待基准设置完成
4. 此角度成为0度基准

### 场景3: 运行中重设
1. 程序运行中
2. 调整传感器到新的期望位置
3. 按R键重新设置基准
4. 新位置成为新的0度基准

## 🔧 键盘控制

| 按键 | 功能 | 说明 |
|------|------|------|
| **R** | 重设基准 | 重新设置初始基准位置 |
| **T** | 共轭四元数 | 改变旋转方向 |
| **Q/W/E** | 轴反向 | 反转X/Y/Z轴方向 |
| **SPACE** | 重置标定 | 重置坐标轴标定参数 |
| **ESC** | 退出 | 关闭程序 |

## 📈 改进效果

### 修复前
- ❌ 微小转动显示为大幅旋转
- ❌ 没有运动基准参考
- ❌ 绝对四元数显示不直观
- ❌ 每次启动位置不一致

### 修复后
- ✅ 微小转动正确显示为微小旋转
- ✅ 有明确的运动基准参考
- ✅ 相对四元数显示直观
- ✅ 每次启动都有一致的基准

## 🎉 使用建议

### 最佳实践
1. **启动时静止**: 程序启动时保持传感器完全静止
2. **合适的基准**: 选择一个方便的角度作为基准位置
3. **及时重设**: 需要时使用R键重新设置基准
4. **观察状态**: 注意界面上的基准设置状态提示

### 注意事项
1. **基准设置期间**: 保持传感器静止不动
2. **数据稳定性**: 确保串口数据正常接收
3. **重设时机**: 在需要新基准时才重设
4. **运动幅度**: 现在可以正确显示各种幅度的运动

---

**现在您的SH5001传感器3D可视化程序具备了完整的初始基准功能，能够准确显示相对于基准位置的所有运动！** 🎯
