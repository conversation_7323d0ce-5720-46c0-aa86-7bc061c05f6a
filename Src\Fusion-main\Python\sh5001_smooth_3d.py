"""
SH5001传感器高性能3D可视化程序
专门优化流畅度和响应性，解决卡顿问题
"""

import sys
import serial
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import time
import threading
import queue
from collections import deque

# 设置matplotlib后端优化
import matplotlib
matplotlib.use('Qt5Agg')  # 使用Qt5后端提高性能
plt.rcParams['animation.html'] = 'none'
plt.rcParams['figure.max_open_warning'] = 0

class SH5001SmoothVisualizer:
    def __init__(self, port='COM6', baudrate=115200):
        """
        高性能SH5001传感器3D可视化器
        专注于流畅度和实时性
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        
        # 高性能数据队列
        self.data_queue = queue.Queue(maxsize=20)  # 限制队列大小
        
        # 当前状态
        self.current_quat = np.array([1.0, 0.0, 0.0, 0.0])
        self.current_rotation_matrix = np.eye(3)

        # 初始基准状态
        self.initial_quat = None  # 初始四元数基准
        self.initial_set = False  # 是否已设置初始基准
        self.calibration_samples = []  # 用于计算稳定的初始状态
        self.calibration_count = 30  # 采集30个样本计算初始状态

        # 相对四元数 (相对于初始状态的旋转)
        self.relative_quat = np.array([1.0, 0.0, 0.0, 0.0])

        # 零漂补偿参数
        self.drift_compensation_enabled = True  # 是否启用零漂补偿
        self.static_threshold = 0.005  # 静止检测阈值
        self.static_samples = []  # 静止状态样本
        self.static_sample_count = 50  # 静止状态样本数量
        self.drift_correction_rate = 0.02  # 漂移修正速率
        self.last_quat = None  # 上一个四元数
        self.motion_variance = 0.0  # 运动方差
        self.is_static = False  # 是否处于静止状态
        self.static_counter = 0  # 静止计数器
        self.drift_reference = None  # 漂移参考四元数

        # 坐标轴标定参数
        self.quat_conjugate = False
        self.axis_invert = {'x': False, 'y': False, 'z': False}
        
        # 性能优化参数
        self.update_interval = 16  # 60FPS (16ms)
        self.skip_frames = 0
        self.frame_count = 0
        
        # 数据统计
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        
        # 设置可视化
        self.setup_visualization()
        
    def setup_visualization(self):
        """设置高性能可视化界面"""
        # 创建图形窗口
        self.fig = plt.figure(figsize=(12, 8))
        self.fig.patch.set_facecolor('black')  # 黑色背景减少渲染负担
        
        # 主3D显示区域
        self.ax_3d = self.fig.add_subplot(111, projection='3d')
        self.ax_3d.set_facecolor('black')
        
        # 设置3D图形属性
        self.ax_3d.set_xlim([-1.2, 1.2])
        self.ax_3d.set_ylim([-1.2, 1.2])
        self.ax_3d.set_zlim([-1.2, 1.2])
        self.ax_3d.set_xlabel('X', color='white')
        self.ax_3d.set_ylabel('Y', color='white')
        self.ax_3d.set_zlabel('Z', color='white')
        
        # 设置坐标轴颜色
        self.ax_3d.xaxis.label.set_color('white')
        self.ax_3d.yaxis.label.set_color('white')
        self.ax_3d.zaxis.label.set_color('white')
        self.ax_3d.tick_params(axis='x', colors='white')
        self.ax_3d.tick_params(axis='y', colors='white')
        self.ax_3d.tick_params(axis='z', colors='white')
        
        # 预创建3D对象以提高性能
        self.setup_3d_objects()
        
        plt.tight_layout()
        
    def setup_3d_objects(self):
        """预创建3D对象"""
        # 坐标轴箭头
        origin = np.array([0, 0, 0])
        
        # X轴 (红色)
        self.x_arrow = self.ax_3d.quiver(0, 0, 0, 1, 0, 0, 
                                        color='red', arrow_length_ratio=0.1, 
                                        linewidth=4, alpha=0.9)
        
        # Y轴 (绿色)
        self.y_arrow = self.ax_3d.quiver(0, 0, 0, 0, 1, 0, 
                                        color='lime', arrow_length_ratio=0.1, 
                                        linewidth=4, alpha=0.9)
        
        # Z轴 (蓝色)
        self.z_arrow = self.ax_3d.quiver(0, 0, 0, 0, 0, 1, 
                                        color='cyan', arrow_length_ratio=0.1, 
                                        linewidth=4, alpha=0.9)
        
        # 传感器立方体边框
        self.cube_lines = []
        self.setup_cube_lines()
        
        # 状态文本
        self.status_text = self.ax_3d.text2D(0.02, 0.98, '', 
                                           transform=self.ax_3d.transAxes,
                                           fontsize=10, color='yellow',
                                           bbox=dict(boxstyle="round,pad=0.3", 
                                                   facecolor="black", alpha=0.7))
        
    def setup_cube_lines(self):
        """设置立方体线框"""
        # 立方体顶点
        size = 0.3
        vertices = np.array([
            [-size, -size, -size], [size, -size, -size], 
            [size, size, -size], [-size, size, -size],
            [-size, -size, size], [size, -size, size], 
            [size, size, size], [-size, size, size]
        ])
        
        # 立方体边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 创建线对象
        for edge in edges:
            line, = self.ax_3d.plot3D([], [], [], 'w-', alpha=0.6, linewidth=1.5)
            self.cube_lines.append((line, edge))
    
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=0.1)
            print(f"✓ 连接串口 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def parse_quaternion(self, line):
        """解析四元数并应用标定"""
        try:
            line = line.strip()
            if not line:
                return None

            values = [float(x.strip()) for x in line.split(',')]
            if len(values) == 4:
                quat = np.array(values, dtype=np.float32)

                # 应用共轭 (旋转方向相反)
                if self.quat_conjugate:
                    quat[1:] = -quat[1:]

                # 归一化
                norm = np.linalg.norm(quat)
                if norm > 0.001:
                    return quat / norm
            return None
        except:
            return None

    def quaternion_multiply(self, q1, q2):
        """四元数乘法"""
        w1, x1, y1, z1 = q1
        w2, x2, y2, z2 = q2

        w = w1*w2 - x1*x2 - y1*y2 - z1*z2
        x = w1*x2 + x1*w2 + y1*z2 - z1*y2
        y = w1*y2 - x1*z2 + y1*w2 + z1*x2
        z = w1*z2 + x1*y2 - y1*x2 + z1*w2

        return np.array([w, x, y, z])

    def quaternion_conjugate(self, q):
        """四元数共轭"""
        w, x, y, z = q
        return np.array([w, -x, -y, -z])

    def set_initial_reference(self, quat):
        """设置初始基准状态"""
        if not self.initial_set:
            # 收集标定样本
            self.calibration_samples.append(quat.copy())

            if len(self.calibration_samples) >= self.calibration_count:
                # 计算平均四元数作为初始基准
                # 简化方法：使用第一个样本作为基准
                self.initial_quat = self.calibration_samples[0].copy()
                self.initial_set = True

                print(f"✓ 初始基准已设置: [{self.initial_quat[0]:.3f}, {self.initial_quat[1]:.3f}, {self.initial_quat[2]:.3f}, {self.initial_quat[3]:.3f}]")
                print("📍 所有后续运动将相对于此基准位置显示")

                # 清空样本数据
                self.calibration_samples.clear()

                return True
            else:
                remaining = self.calibration_count - len(self.calibration_samples)
                if remaining % 5 == 0:  # 每5个样本提示一次
                    print(f"🔄 正在设置初始基准... 还需 {remaining} 个样本")
                return False
        return True

    def calculate_relative_quaternion(self, current_quat):
        """计算相对于初始状态的四元数"""
        if self.initial_quat is None:
            return current_quat

        # 计算相对四元数: q_relative = q_current * q_initial^(-1)
        initial_conjugate = self.quaternion_conjugate(self.initial_quat)
        relative_quat = self.quaternion_multiply(current_quat, initial_conjugate)

        return relative_quat

    def detect_static_state(self, quat):
        """检测传感器是否处于静止状态"""
        if self.last_quat is None:
            self.last_quat = quat.copy()
            return False

        # 计算四元数变化量
        quat_diff = quat - self.last_quat
        motion_magnitude = np.linalg.norm(quat_diff)

        # 更新运动方差 (指数移动平均)
        alpha = 0.1
        self.motion_variance = alpha * motion_magnitude + (1 - alpha) * self.motion_variance

        # 判断是否静止
        if self.motion_variance < self.static_threshold:
            self.static_counter += 1
            if self.static_counter > 20:  # 连续20帧静止才认为真正静止
                self.is_static = True
        else:
            self.static_counter = 0
            self.is_static = False

        self.last_quat = quat.copy()
        return self.is_static

    def apply_drift_compensation(self, quat):
        """应用零漂补偿"""
        if not self.drift_compensation_enabled:
            return quat

        # 检测静止状态
        is_static = self.detect_static_state(quat)

        if is_static:
            # 收集静止状态样本
            self.static_samples.append(quat.copy())

            # 限制样本数量
            if len(self.static_samples) > self.static_sample_count:
                self.static_samples.pop(0)

            # 如果有足够的静止样本，计算漂移参考
            if len(self.static_samples) >= 10:
                # 使用最近的静止样本作为漂移参考
                self.drift_reference = np.mean(self.static_samples[-10:], axis=0)
                self.drift_reference = self.drift_reference / np.linalg.norm(self.drift_reference)

        # 应用漂移修正
        if self.drift_reference is not None and is_static:
            # 在静止状态下，缓慢修正到漂移参考
            correction_quat = self.slerp(quat, self.drift_reference, self.drift_correction_rate)
            return correction_quat

        return quat

    def slerp(self, q1, q2, t):
        """球面线性插值 (Spherical Linear Interpolation)"""
        # 确保四元数归一化
        q1 = q1 / np.linalg.norm(q1)
        q2 = q2 / np.linalg.norm(q2)

        # 计算点积
        dot = np.dot(q1, q2)

        # 如果点积为负，取反其中一个四元数以选择较短路径
        if dot < 0.0:
            q2 = -q2
            dot = -dot

        # 如果四元数非常接近，使用线性插值
        if dot > 0.9995:
            result = q1 + t * (q2 - q1)
            return result / np.linalg.norm(result)

        # 计算角度
        theta_0 = np.arccos(np.abs(dot))
        sin_theta_0 = np.sin(theta_0)

        theta = theta_0 * t
        sin_theta = np.sin(theta)

        s0 = np.cos(theta) - dot * sin_theta / sin_theta_0
        s1 = sin_theta / sin_theta_0

        return s0 * q1 + s1 * q2
    
    def quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        w, x, y, z = quat
        
        # 应用轴反向
        if self.axis_invert['x']:
            x = -x
        if self.axis_invert['y']:
            y = -y
        if self.axis_invert['z']:
            z = -z
        
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])
    
    def read_serial_thread(self):
        """高性能串口读取线程"""
        buffer = ""
        
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data
                    
                    lines = buffer.split('\n')
                    buffer = lines[-1]
                    
                    for line in lines[:-1]:
                        quat = self.parse_quaternion(line)
                        if quat is not None:
                            # 非阻塞放入队列
                            try:
                                self.data_queue.put_nowait((time.time(), quat))
                            except queue.Full:
                                # 队列满时丢弃最旧的数据
                                try:
                                    self.data_queue.get_nowait()
                                    self.data_queue.put_nowait((time.time(), quat))
                                except queue.Empty:
                                    pass
                
                time.sleep(0.001)  # 1ms延迟
                
            except Exception as e:
                print(f"串口错误: {e}")
                time.sleep(0.1)
    
    def update_visualization(self, frame):
        """高性能可视化更新"""
        # 更新FPS计数
        self.fps_counter += 1
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.last_fps_time = current_time
        
        # 处理新数据
        data_updated = False
        while not self.data_queue.empty():
            try:
                timestamp, quat = self.data_queue.get_nowait()

                # 应用零漂补偿
                compensated_quat = self.apply_drift_compensation(quat)

                # 设置初始基准
                if not self.set_initial_reference(compensated_quat):
                    continue  # 还在收集初始基准样本

                # 计算相对四元数
                self.relative_quat = self.calculate_relative_quaternion(compensated_quat)
                self.current_quat = quat  # 保存原始四元数用于显示

                # 使用相对四元数计算旋转矩阵
                self.current_rotation_matrix = self.quaternion_to_rotation_matrix(self.relative_quat)
                data_updated = True
            except queue.Empty:
                break
        
        # 只在有新数据时更新显示
        if data_updated or self.frame_count % 30 == 0:  # 强制每30帧更新一次
            self.update_3d_objects()
        
        self.frame_count += 1
        
    def update_3d_objects(self):
        """更新3D对象"""
        R = self.current_rotation_matrix
        
        # 更新坐标轴
        origin = np.array([0, 0, 0])
        x_axis = R @ np.array([1, 0, 0])
        y_axis = R @ np.array([0, 1, 0])
        z_axis = R @ np.array([0, 0, 1])
        
        # 移除旧的箭头并创建新的
        self.ax_3d.clear()
        self.ax_3d.set_xlim([-1.2, 1.2])
        self.ax_3d.set_ylim([-1.2, 1.2])
        self.ax_3d.set_zlim([-1.2, 1.2])
        self.ax_3d.set_facecolor('black')
        
        # 绘制坐标轴
        self.ax_3d.quiver(0, 0, 0, x_axis[0], x_axis[1], x_axis[2], 
                         color='red', arrow_length_ratio=0.1, linewidth=4, alpha=0.9)
        self.ax_3d.quiver(0, 0, 0, y_axis[0], y_axis[1], y_axis[2], 
                         color='lime', arrow_length_ratio=0.1, linewidth=4, alpha=0.9)
        self.ax_3d.quiver(0, 0, 0, z_axis[0], z_axis[1], z_axis[2], 
                         color='cyan', arrow_length_ratio=0.1, linewidth=4, alpha=0.9)
        
        # 绘制立方体
        self.draw_cube(R)
        
        # 更新状态文本
        w, x, y, z = self.current_quat
        rw, rx, ry, rz = self.relative_quat

        status_lines = [f"FPS: {self.current_fps}"]

        if not self.initial_set:
            remaining = self.calibration_count - len(self.calibration_samples)
            status_lines.append(f"🔄 设置基准: {remaining}")
        else:
            status_lines.append("✓ 基准已设置")

        # 零漂补偿状态
        drift_status = "ON" if self.drift_compensation_enabled else "OFF"
        static_status = "静止" if self.is_static else "运动"
        status_lines.append(f"零漂补偿: {drift_status}")
        status_lines.append(f"状态: {static_status}")
        status_lines.append(f"运动方差: {self.motion_variance:.4f}")

        status_lines.extend([
            f"原始Q: [{w:.2f}, {x:.2f}, {y:.2f}, {z:.2f}]",
            f"相对Q: [{rw:.2f}, {rx:.2f}, {ry:.2f}, {rz:.2f}]",
            f"共轭: {'ON' if self.quat_conjugate else 'OFF'}",
            f"X反向: {'ON' if self.axis_invert['x'] else 'OFF'}",
            f"Y反向: {'ON' if self.axis_invert['y'] else 'OFF'}",
            f"Z反向: {'ON' if self.axis_invert['z'] else 'OFF'}"
        ])

        status = '\n'.join(status_lines)
        
        self.ax_3d.text2D(0.02, 0.98, status, transform=self.ax_3d.transAxes,
                         fontsize=9, color='yellow', verticalalignment='top',
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="black", alpha=0.7))
    
    def draw_cube(self, R):
        """绘制传感器立方体"""
        size = 0.3
        vertices = np.array([
            [-size, -size, -size], [size, -size, -size], 
            [size, size, -size], [-size, size, -size],
            [-size, -size, size], [size, -size, size], 
            [size, size, size], [-size, size, size]
        ])
        
        # 应用旋转
        rotated_vertices = vertices @ R.T
        
        # 立方体边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 绘制边
        for edge in edges:
            points = rotated_vertices[edge]
            self.ax_3d.plot3D(*points.T, 'w-', alpha=0.6, linewidth=1.5)
        
        # 标记正面
        front_center = R @ np.array([size*0.8, 0, 0])
        self.ax_3d.scatter(*front_center, color='red', s=30, alpha=0.8)
    
    def on_key_press(self, event):
        """键盘事件处理"""
        if event.key == 'escape':
            self.running = False
            plt.close('all')
        elif event.key == 't':
            self.quat_conjugate = not self.quat_conjugate
            print(f"共轭四元数: {self.quat_conjugate}")
        elif event.key == 'q':
            self.axis_invert['x'] = not self.axis_invert['x']
            print(f"X轴反向: {self.axis_invert['x']}")
        elif event.key == 'w':
            self.axis_invert['y'] = not self.axis_invert['y']
            print(f"Y轴反向: {self.axis_invert['y']}")
        elif event.key == 'e':
            self.axis_invert['z'] = not self.axis_invert['z']
            print(f"Z轴反向: {self.axis_invert['z']}")
        elif event.key == 'r':
            # 重新设置基准
            self.reset_initial_reference()
        elif event.key == 'd':
            # 切换零漂补偿
            self.drift_compensation_enabled = not self.drift_compensation_enabled
            print(f"零漂补偿: {'启用' if self.drift_compensation_enabled else '禁用'}")
        elif event.key == 'c':
            # 清除零漂补偿数据
            self.clear_drift_compensation()
        elif event.key == ' ':
            # 重置标定参数
            self.quat_conjugate = False
            self.axis_invert = {'x': False, 'y': False, 'z': False}
            print("标定参数已重置")

    def reset_initial_reference(self):
        """重新设置初始基准"""
        self.initial_quat = None
        self.initial_set = False
        self.calibration_samples.clear()
        self.relative_quat = np.array([1.0, 0.0, 0.0, 0.0])
        print("🔄 重新设置初始基准，请保持传感器静止...")

    def clear_drift_compensation(self):
        """清除零漂补偿数据"""
        self.static_samples.clear()
        self.drift_reference = None
        self.motion_variance = 0.0
        self.is_static = False
        self.static_counter = 0
        self.last_quat = None
        print("🧹 零漂补偿数据已清除")
    
    def start(self):
        """启动高性能可视化"""
        print("🚀 启动SH5001高性能3D可视化 (带初始基准)")
        print(f"📡 串口: {self.port}, 波特率: {self.baudrate}")
        print("⚡ 性能优化: 60FPS目标")
        print("📍 初始基准: 程序启动时自动设置基准位置")
        print("🎮 控制键:")
        print("   T - 共轭四元数 (旋转方向)")
        print("   Q/W/E - X/Y/Z轴反向")
        print("   R - 重新设置基准位置")
        print("   SPACE - 重置标定")
        print("   ESC - 退出")
        print("\n📋 使用说明:")
        print("1. 程序启动后，请保持传感器静止3秒")
        print("2. 系统将自动记录当前位置作为基准")
        print("3. 之后所有运动都相对于此基准显示")
        
        if not self.connect_serial():
            print("❌ 串口连接失败")
            return
        
        self.running = True
        
        # 设置键盘事件
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)
        
        # 启动串口线程
        serial_thread = threading.Thread(target=self.read_serial_thread)
        serial_thread.daemon = True
        serial_thread.start()
        
        # 启动高性能动画
        self.animation = animation.FuncAnimation(
            self.fig, self.update_visualization,
            interval=self.update_interval, blit=False, cache_frame_data=False
        )
        
        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n⏹️ 程序停止")
        finally:
            self.running = False
            if self.ser:
                self.ser.close()

def test_mode():
    """测试模式"""
    print("🧪 高性能测试模式")
    
    import math
    
    class TestVisualizer(SH5001SmoothVisualizer):
        def __init__(self):
            super().__init__()
            self.test_time = 0

        def connect_serial(self):
            print("✓ 测试模式 - 将演示微小旋转效果")
            return True

        def read_serial_thread(self):
            while self.running:
                t = self.test_time * 0.01  # 减慢旋转速度

                # 微小的摆动旋转，模拟真实的小幅度运动
                angle_amplitude = 0.2  # 减小旋转幅度
                angle_z = math.sin(t) * angle_amplitude
                angle_x = math.cos(t * 0.7) * angle_amplitude * 0.5
                angle_y = math.sin(t * 0.3) * angle_amplitude * 0.3

                # 使用小角度近似
                w = math.cos(angle_z/2) * math.cos(angle_x/2) * math.cos(angle_y/2)
                x = math.sin(angle_x/2) * math.cos(angle_z/2) * math.cos(angle_y/2)
                y = math.sin(angle_y/2) * math.cos(angle_z/2) * math.cos(angle_x/2)
                z = math.sin(angle_z/2) * math.cos(angle_x/2) * math.cos(angle_y/2)

                quat = np.array([w, x, y, z])
                quat = quat / np.linalg.norm(quat)

                try:
                    self.data_queue.put_nowait((time.time(), quat))
                except queue.Full:
                    pass

                self.test_time += 1
                time.sleep(0.02)  # 50Hz
    
    visualizer = TestVisualizer()
    visualizer.start()

def main():
    """主函数"""
    print("⚡ SH5001高性能3D可视化程序")
    
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        test_mode()
    else:
        try:
            visualizer = SH5001SmoothVisualizer(port='COM6', baudrate=115200)
            visualizer.start()
        except Exception as e:
            print(f"❌ 错误: {e}")
            print("💡 尝试测试模式: python sh5001_smooth_3d.py --test")

if __name__ == "__main__":
    main()
