#!/usr/bin/env python3
"""
SH5001传感器3D可视化系统安装和运行脚本
"""

import subprocess
import sys
import os
import importlib

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def install_imufusion():
    """安装Imufusion库"""
    print("🔧 正在安装Imufusion库...")
    
    # 首先尝试从当前目录安装
    current_dir = os.path.dirname(os.path.abspath(__file__))
    fusion_dir = os.path.join(current_dir, "..")
    
    if os.path.exists(os.path.join(fusion_dir, "setup.py")):
        try:
            print("📁 从本地源码安装Imufusion...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", fusion_dir])
            print("✓ Imufusion 本地安装成功")
            return True
        except subprocess.CalledProcessError:
            print("⚠️ 本地安装失败，尝试从PyPI安装...")
    
    # 尝试从PyPI安装
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "imufusion"])
        print("✓ Imufusion PyPI安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ Imufusion 安装失败")
        return False

def check_serial_ports():
    """检查可用的串口"""
    try:
        import serial.tools.list_ports
        ports = list(serial.tools.list_ports.comports())
        print("\n📡 可用串口:")
        for port in ports:
            print(f"  - {port.device}: {port.description}")
        
        # 检查COM6是否存在
        com6_exists = any(port.device == 'COM6' for port in ports)
        if com6_exists:
            print("✓ COM6 端口可用")
        else:
            print("⚠️ COM6 端口未找到，请检查设备连接")
        
        return len(ports) > 0
    except Exception as e:
        print(f"❌ 串口检查失败: {e}")
        return False

def install_dependencies():
    """安装所有依赖"""
    print("🔧 正在检查和安装依赖包...")
    
    dependencies = [
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("pyserial", "serial"),
        ("scipy", "scipy"),
    ]
    
    all_installed = True
    
    for package, import_name in dependencies:
        if not install_package(package, import_name):
            all_installed = False
    
    # 安装Imufusion
    if not install_imufusion():
        all_installed = False
    
    return all_installed

def test_imufusion():
    """测试Imufusion库"""
    try:
        import imufusion
        print("✓ Imufusion 导入成功")
        
        # 创建AHRS对象测试
        ahrs = imufusion.Ahrs()
        print("✓ AHRS 对象创建成功")
        
        # 测试基本功能
        import numpy as np
        gyro = np.array([0.0, 0.0, 0.0])
        accel = np.array([0.0, 0.0, 1.0])
        ahrs.update_no_magnetometer(gyro, accel, 0.01)
        q = ahrs.quaternion
        print(f"✓ 四元数测试: [{q.w:.3f}, {q.x:.3f}, {q.y:.3f}, {q.z:.3f}]")
        
        return True
    except Exception as e:
        print(f"❌ Imufusion 测试失败: {e}")
        return False

def run_visualization():
    """运行可视化程序"""
    print("\n🚀 启动SH5001 3D可视化程序...")
    
    try:
        # 导入并运行主程序
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        from sh5001_fusion_3d import main
        main()
        
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
        import traceback
        traceback.print_exc()

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("📋 SH5001传感器3D可视化系统使用说明")
    print("="*60)
    print("1. 硬件连接:")
    print("   - 确保SH5001传感器连接到ESP32S3开发板")
    print("   - 通过USB将ESP32S3连接到电脑")
    print("   - 确认串口为COM6，波特率115200")
    print()
    print("2. 数据格式:")
    print("   - 四元数格式: w,x,y,z")
    print("   - 示例: -0.0227,0.8463,-0.5212,-0.1077")
    print()
    print("3. 功能特性:")
    print("   - 实时3D姿态可视化")
    print("   - Imufusion滤波对比")
    print("   - 四元数和欧拉角曲线")
    print("   - 数据质量监控")
    print()
    print("4. 操作说明:")
    print("   - 程序启动后会自动连接COM6串口")
    print("   - 实时显示传感器姿态和数据曲线")
    print("   - 按Ctrl+C或关闭窗口退出程序")
    print("="*60)

def main():
    """主函数"""
    print("🎯 SH5001传感器3D可视化系统安装程序")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请检查网络连接和权限")
        return
    
    # 测试Imufusion
    if not test_imufusion():
        print("❌ Imufusion测试失败")
        return
    
    # 检查串口
    check_serial_ports()
    
    print("\n✅ 所有依赖安装完成！")
    
    # 显示使用说明
    show_usage_instructions()
    
    # 询问是否立即运行
    try:
        response = input("\n🚀 是否立即启动可视化程序? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '']:
            run_visualization()
        else:
            print("💡 稍后可以运行 'python sh5001_fusion_3d.py' 启动程序")
    except KeyboardInterrupt:
        print("\n👋 安装完成，程序退出")

if __name__ == "__main__":
    main()
