import sys
import serial
import j<PERSON>
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QOpenGLWidget
from PyQt5.QtCore import QTimer
from OpenGL.GL import *
from OpenGL.GLU import *
import imufusion

buffer = ''
ser = serial.Serial('COM4', 115200, timeout=1, write_timeout=1, inter_byte_timeout=0.01)
ahrs = imufusion.Ahrs()
gyro_values = [0, 0, 0]
accel_values = [0, 0, 0]
quaternions = np.empty((100000000, 4), dtype=np.float32)
index = 0

class OpenGLWidget(QOpenGLWidget):
    def __init__(self, parent=None):
        super(OpenGLWidget, self).__init__(parent)
        self.timer = QTimer()
        self.timer.timeout.connect(self.update)  # 每 10ms 更新一次
        self.timer.start(10)
    
    def initializeGL(self):
        glClearColor(0.0, 0.0, 0.0, 1.0)
        glEnable(GL_DEPTH_TEST)
    
    def resizeGL(self, w, h):
        glViewport(0, 0, w, h)
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        gluPerspective(45, w / h, 0.1, 50.0)
        glMatrixMode(GL_MODELVIEW)
    
    def paintGL(self):
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        glLoadIdentity()
        update_rotation()
        glTranslatef(0.0, 0.0, -5.0)
        apply_cube_rotation()
        draw_cube()

def draw_cube():
    glBegin(GL_QUADS)
    faces = [
        ([1.0, 0.0, 0.0], [-1, -1, 1], [1, -1, 1], [1, 1, 1], [-1, 1, 1]),
        ([0.0, 1.0, 0.0], [-1, -1, -1], [-1, 1, -1], [1, 1, -1], [1, -1, -1]),
        ([0.0, 0.0, 1.0], [-1, 1, -1], [-1, 1, 1], [1, 1, 1], [1, 1, -1]),
        ([1.0, 1.0, 0.0], [-1, -1, -1], [1, -1, -1], [1, -1, 1], [-1, -1, 1]),
        ([1.0, 0.0, 1.0], [-1, -1, -1], [-1, -1, 1], [-1, 1, 1], [-1, 1, -1]),
        ([0.0, 1.0, 1.0], [1, -1, -1], [1, 1, -1], [1, 1, 1], [1, -1, 1])
    ]
    for color, v1, v2, v3, v4 in faces:
        glColor3f(*color)
        glVertex3f(*v1)
        glVertex3f(*v2)
        glVertex3f(*v3)
        glVertex3f(*v4)
    glEnd()

def update_quat():
    global gyro_values, accel_values, quaternions, index
    gyro_values = np.array(gyro_values, dtype=np.float32)
    print(gyro_values)
    accel_values = np.array(accel_values, dtype=np.float32)
    ahrs.update_no_magnetometer(gyro_values, accel_values, 1.0 / 100)
    index += 1
    q = ahrs.quaternion
    print(q.w)
    quaternions[index] = np.array([q.w, q.x, q.y, q.z])

def update_rotation():
    global buffer
    if ser.in_waiting > 0:
        line = ser.read(ser.in_waiting).decode('utf-8')
        buffer += line
    try:
        while True:
            start = buffer.find("{")
            end = buffer.find("}", start)
            if start != -1 and end != -1:
                data = json.loads(buffer[start:end + 1])
                buffer = buffer[end + 1:]
                global gyro_values, accel_values
                accel_values = np.array(data['ACCEL'], dtype=np.float32)
                gyro_values = np.array(data['GYRO'], dtype=np.float32)
                update_quat()
            else:
                break
    except (json.JSONDecodeError, KeyError) as e:
        print("解析错误:", e, "buffer:", buffer)

def apply_cube_rotation():
    global quaternions, index
    w, x, y, z = quaternions[index]
    print(w, x, y, z)
    angle = np.degrees(2 * np.arccos(w))
    norm = np.sqrt(1 - w * w)
    if norm > 0:
        glRotatef(angle, x / norm, y / norm, z / norm)

def main():
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("Quaternion Rotation with OpenGL")
    window.setGeometry(100, 100, 800, 600)
    openGLWidget = OpenGLWidget(window)
    window.setCentralWidget(openGLWidget)
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
