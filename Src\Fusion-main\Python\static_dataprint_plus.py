# 数据文件路径
file_path = r"D:\pythonME\Fusion-main\Fusion-main\Python\static_sensor_data.txt"

# 读取并过滤数据
with open(file_path, 'r') as file:
    lines = file.readlines()  # 读取所有行

# 打开文件准备写入筛选后的数据
with open(file_path, 'w') as file:
    for line in lines:
        values = line.strip().split(",")  # 假设每行数据是逗号分隔的

        if len(values) > 0:  # 确保数据有效
            try:
                # 获取每行第一个数值并转换为浮动点数
                first_value = float(values[0])

                # 如果第一个值大于1000000，跳过该行
                if first_value > 1000000:
                    print(f"丢弃数据: {line.strip()}")
                    continue  # 跳过该行

            except ValueError:
                print(f"解析错误，跳过该行: {line.strip()}")
                continue  # 跳过无法解析的行

        # 如果该行数据有效，则写入文件
        file.write(line)

print("数据过滤完成，文件已更新。")
