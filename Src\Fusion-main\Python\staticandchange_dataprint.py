import serial
import time

# 配置串口参数
port = 'COM4'  # 根据你的实际端口号修改
baud_rate = 115200

# 打开串口
ser = serial.Serial(port, baud_rate, timeout=1)


# 桌面文件路径
file_path = r"D:\pythonME\Fusion-main\Fusion-main\Python\staticandchange_sensor_data.txt"

# 打开文件，准备写入数据
with open(file_path, 'w') as file:
    try:
        while True:
            if ser.in_waiting > 0:
                line = ser.readline().decode('utf-8').strip() # ser.readline()从串行端口读取一行数据，返回的是bytes.decode('utf-8')将字节类型解码为字符串（str）数据是以UTF-8编码的.strip()去除字符串两端的空白字符（包括换行符\n和空格）。
                # print(ser.readline())
                # print(ser.readline().decode('utf-8'))
                print(line)  # 打印到控制台
                file.write(line + '\n')  # 写入文件
    except KeyboardInterrupt:
        print("数据收集完成，文件已保存")
