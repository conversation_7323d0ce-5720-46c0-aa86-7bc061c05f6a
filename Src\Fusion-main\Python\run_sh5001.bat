@echo off
echo ========================================
echo SH5001传感器3D可视化系统启动器
echo ========================================
echo.

echo 请选择运行模式:
echo 1. 高性能版本 (推荐 - 流畅60FPS)
echo 2. 简化版本 (标准功能)
echo 3. 完整版本 (包含Imufusion滤波)
echo 4. 测试模式 (模拟数据)
echo 5. 坐标轴标定工具
echo 6. 安装依赖
echo 7. 退出
echo.

set /p choice=请输入选择 (1-7):

if "%choice%"=="1" (
    echo 启动高性能版本...
    python sh5001_smooth_3d.py
) else if "%choice%"=="2" (
    echo 启动简化版本...
    python sh5001_simple_3d.py
) else if "%choice%"=="3" (
    echo 启动完整版本...
    python sh5001_fusion_3d.py
) else if "%choice%"=="4" (
    echo 启动测试模式...
    python sh5001_smooth_3d.py --test
) else if "%choice%"=="5" (
    echo 启动坐标轴标定工具...
    python sh5001_calibration_config.py
) else if "%choice%"=="6" (
    echo 安装依赖...
    python setup_and_run.py
) else if "%choice%"=="7" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，请重新运行
)

echo.
pause
