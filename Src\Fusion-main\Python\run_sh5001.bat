@echo off
echo ========================================
echo SH5001传感器3D可视化系统启动器
echo ========================================
echo.

echo 请选择运行模式:
echo 1. 简化版本 (推荐)
echo 2. 完整版本 (包含Imufusion滤波)
echo 3. 测试模式 (模拟数据)
echo 4. 安装依赖
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动简化版本...
    python sh5001_simple_3d.py
) else if "%choice%"=="2" (
    echo 启动完整版本...
    python sh5001_fusion_3d.py
) else if "%choice%"=="3" (
    echo 启动测试模式...
    python sh5001_simple_3d.py --test
) else if "%choice%"=="4" (
    echo 安装依赖...
    python setup_and_run.py
) else if "%choice%"=="5" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，请重新运行
)

echo.
pause
