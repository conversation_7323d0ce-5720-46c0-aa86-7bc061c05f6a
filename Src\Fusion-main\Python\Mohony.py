import numpy as np
from ahrs.filters import Mahony
import matplotlib.pyplot as plt

# 读取传感器数据
data = np.genfromtxt(r"D:\pythonME\Fusion-main\Fusion-main\Python\static_sensor_data.txt", delimiter=",", skip_header=1)

timestamp = data[:, 0]
gyroscope = data[:, 1:4]  # 陀螺仪数据 (rad/s)
accelerometer = data[:, 4:7]  # 加速度计数据 (m/s²)

# 初始化 Mahony 滤波器
mahony = Mahony(kp=1.0, ki=0.01, frequency=100)  # 采样率 100 Hz

# 存储四元数
quaternions = []
for gyr, acc in zip(gyroscope, accelerometer):
    q = mahony.updateIMU(gyr=gyr, acc=acc)  # 注意方法名是 updateIMU，不是 update
    quaternions.append(q)
quaternions = np.array(quaternions)  # 形状 (N, 4)

# 四元数转欧拉角 (roll, pitch, yaw)
def quat_to_euler(q):
    roll = np.arctan2(2*(q[0]*q[1] + q[2]*q[3]), 1 - 2*(q[1]**2 + q[2]**2))
    pitch = np.arcsin(2*(q[0]*q[2] - q[3]*q[1]))
    yaw = np.arctan2(2*(q[0]*q[3] + q[1]*q[2]), 1 - 2*(q[2]**2 + q[3]**2))
    return np.degrees([roll, pitch, yaw])  # 转为角度

euler_angles = np.array([quat_to_euler(q) for q in quaternions])

# 绘制四元数曲线
plt.figure(figsize=(12, 6))
plt.plot(timestamp, quaternions[:, 0], "tab:red", label="W")
plt.plot(timestamp, quaternions[:, 1], "tab:green", label="X")
plt.plot(timestamp, quaternions[:, 2], "tab:blue", label="Y")
plt.plot(timestamp, quaternions[:, 3], "tab:purple", label="Z")
plt.title("Mahony Filter - Quaternion Output")
plt.xlabel("Time (ms)")
plt.ylabel("Value")
plt.legend()
plt.grid()
plt.show()

# 绘制欧拉角曲线
plt.figure(figsize=(12, 6))
plt.plot(timestamp, euler_angles[:, 0], "tab:red", label="Roll")
plt.plot(timestamp, euler_angles[:, 1], "tab:green", label="Pitch")
plt.plot(timestamp, euler_angles[:, 2], "tab:blue", label="Yaw")
plt.title("Mahony Filter - Euler Angles")
plt.xlabel("Time (ms)")
plt.ylabel("Angle (degrees)")
plt.legend()
plt.grid()
plt.show()