"""
SH5001传感器坐标轴标定配置管理
用于保存和加载坐标轴标定参数
"""

import json
import os
from typing import Dict, List, Any

class CalibrationConfig:
    """坐标轴标定配置管理类"""
    
    def __init__(self, config_file='sh5001_calibration.json'):
        self.config_file = config_file
        self.default_config = {
            'axis_mapping': {'x': 0, 'y': 1, 'z': 2},  # 轴映射
            'axis_invert': {'x': False, 'y': False, 'z': False},  # 轴反向
            'quat_order': [0, 1, 2, 3],  # 四元数顺序 [w,x,y,z]
            'quat_conjugate': False,  # 是否使用共轭四元数
            'description': '默认配置',
            'created_time': '',
        }
        
    def save_config(self, config: Dict[str, Any], description: str = '') -> bool:
        """保存标定配置"""
        try:
            import datetime
            config_data = config.copy()
            config_data['description'] = description
            config_data['created_time'] = datetime.datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 标定配置已保存到: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def load_config(self) -> Dict[str, Any]:
        """加载标定配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✓ 已加载标定配置: {config.get('description', '未知')}")
                return config
            else:
                print("ℹ️ 配置文件不存在，使用默认配置")
                return self.default_config.copy()
                
        except Exception as e:
            print(f"❌ 加载配置失败: {e}，使用默认配置")
            return self.default_config.copy()
    
    def get_preset_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取预设配置"""
        presets = {
            'default': {
                'axis_mapping': {'x': 0, 'y': 1, 'z': 2},
                'axis_invert': {'x': False, 'y': False, 'z': False},
                'quat_order': [0, 1, 2, 3],
                'quat_conjugate': False,
                'description': '默认配置 (w,x,y,z)',
            },
            'conjugate': {
                'axis_mapping': {'x': 0, 'y': 1, 'z': 2},
                'axis_invert': {'x': False, 'y': False, 'z': False},
                'quat_order': [0, 1, 2, 3],
                'quat_conjugate': True,
                'description': '共轭四元数 (旋转方向相反)',
            },
            'xyz_inverted': {
                'axis_mapping': {'x': 0, 'y': 1, 'z': 2},
                'axis_invert': {'x': True, 'y': True, 'z': True},
                'quat_order': [0, 1, 2, 3],
                'quat_conjugate': False,
                'description': 'XYZ轴全部反向',
            },
            'xyzw_order': {
                'axis_mapping': {'x': 0, 'y': 1, 'z': 2},
                'axis_invert': {'x': False, 'y': False, 'z': False},
                'quat_order': [1, 2, 3, 0],  # x,y,z,w
                'quat_conjugate': False,
                'description': '四元数顺序: x,y,z,w',
            },
            'right_hand_to_left_hand': {
                'axis_mapping': {'x': 0, 'y': 1, 'z': 2},
                'axis_invert': {'x': False, 'y': False, 'z': True},
                'quat_order': [0, 1, 2, 3],
                'quat_conjugate': True,
                'description': '右手坐标系转左手坐标系',
            },
            'sensor_rotated_90': {
                'axis_mapping': {'x': 1, 'y': 0, 'z': 2},  # X->Y, Y->X
                'axis_invert': {'x': False, 'y': True, 'z': False},
                'quat_order': [0, 1, 2, 3],
                'quat_conjugate': False,
                'description': '传感器旋转90度安装',
            },
        }
        return presets
    
    def apply_preset(self, preset_name: str) -> Dict[str, Any]:
        """应用预设配置"""
        presets = self.get_preset_configs()
        if preset_name in presets:
            config = presets[preset_name].copy()
            print(f"✓ 应用预设配置: {config['description']}")
            return config
        else:
            print(f"❌ 预设配置 '{preset_name}' 不存在")
            return self.default_config.copy()
    
    def print_current_config(self, config: Dict[str, Any]):
        """打印当前配置"""
        print("\n" + "="*50)
        print("📋 当前标定配置")
        print("="*50)
        
        # 轴映射
        mapping = config['axis_mapping']
        axis_names = ['X', 'Y', 'Z']
        print("🔄 轴映射:")
        for axis, target in mapping.items():
            print(f"   {axis.upper()}轴 -> {axis_names[target]}轴")
        
        # 轴反向
        invert = config['axis_invert']
        print("🔀 轴反向:")
        for axis, inverted in invert.items():
            status = "反向" if inverted else "正向"
            print(f"   {axis.upper()}轴: {status}")
        
        # 四元数顺序
        order = config['quat_order']
        order_map = {0: 'w', 1: 'x', 2: 'y', 3: 'z'}
        order_str = ','.join([order_map[i] for i in order])
        print(f"📊 四元数顺序: {order_str}")
        
        # 共轭
        conjugate = config['quat_conjugate']
        print(f"🔄 共轭四元数: {'是' if conjugate else '否'}")
        
        # 描述
        if 'description' in config:
            print(f"📝 描述: {config['description']}")
        
        print("="*50)
    
    def interactive_calibration(self) -> Dict[str, Any]:
        """交互式标定配置"""
        print("\n🎯 SH5001传感器坐标轴标定向导")
        print("="*50)
        
        config = self.default_config.copy()
        
        # 选择预设或自定义
        print("\n1. 使用预设配置")
        print("2. 自定义配置")
        choice = input("请选择 (1/2): ").strip()
        
        if choice == '1':
            presets = self.get_preset_configs()
            print("\n可用预设:")
            for i, (name, preset) in enumerate(presets.items(), 1):
                print(f"{i}. {name}: {preset['description']}")
            
            try:
                preset_idx = int(input("选择预设 (输入数字): ")) - 1
                preset_names = list(presets.keys())
                if 0 <= preset_idx < len(preset_names):
                    config = presets[preset_names[preset_idx]].copy()
                    print(f"✓ 已选择预设: {config['description']}")
            except (ValueError, IndexError):
                print("❌ 无效选择，使用默认配置")
        
        elif choice == '2':
            # 自定义配置
            print("\n🔧 自定义配置")
            
            # 四元数顺序
            print("\n四元数顺序:")
            print("1. w,x,y,z (默认)")
            print("2. x,y,z,w")
            print("3. z,w,x,y")
            order_choice = input("选择顺序 (1-3): ").strip()
            
            if order_choice == '2':
                config['quat_order'] = [1, 2, 3, 0]
            elif order_choice == '3':
                config['quat_order'] = [3, 0, 1, 2]
            
            # 共轭四元数
            conjugate = input("使用共轭四元数? (y/n): ").strip().lower()
            config['quat_conjugate'] = conjugate in ['y', 'yes', '是']
            
            # 轴反向
            for axis in ['x', 'y', 'z']:
                invert = input(f"{axis.upper()}轴反向? (y/n): ").strip().lower()
                config['axis_invert'][axis] = invert in ['y', 'yes', '是']
        
        # 添加描述
        description = input("\n配置描述 (可选): ").strip()
        if description:
            config['description'] = description
        
        # 显示最终配置
        self.print_current_config(config)
        
        # 确认保存
        save = input("\n保存此配置? (y/n): ").strip().lower()
        if save in ['y', 'yes', '是']:
            self.save_config(config, config.get('description', ''))
        
        return config

def main():
    """主函数 - 标定配置工具"""
    print("🎯 SH5001传感器标定配置工具")
    
    calibration = CalibrationConfig()
    
    while True:
        print("\n" + "="*40)
        print("📋 标定配置菜单")
        print("="*40)
        print("1. 交互式标定配置")
        print("2. 查看当前配置")
        print("3. 加载配置")
        print("4. 查看预设配置")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            config = calibration.interactive_calibration()
        elif choice == '2':
            config = calibration.load_config()
            calibration.print_current_config(config)
        elif choice == '3':
            config = calibration.load_config()
            print("✓ 配置已加载")
        elif choice == '4':
            presets = calibration.get_preset_configs()
            print("\n📚 预设配置:")
            for name, preset in presets.items():
                print(f"\n🔧 {name}:")
                calibration.print_current_config(preset)
        elif choice == '5':
            print("👋 退出配置工具")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
