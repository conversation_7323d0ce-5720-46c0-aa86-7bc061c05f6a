# SH5001传感器3D可视化系统

本项目为SH5001 IMU传感器提供实时3D可视化功能，支持Imufusion数据滤波和多种可视化模式。

## 🎯 项目特性

- **实时3D姿态可视化**: 基于四元数的实时3D姿态显示
- **Imufusion滤波**: 集成Imufusion库进行数据滤波和对比
- **多图表显示**: 四元数曲线、欧拉角曲线、数据状态监控
- **串口通信**: 支持ESP32S3开发板串口数据接收
- **数据格式兼容**: 支持标准四元数格式输入

## 📋 系统要求

### 硬件要求
- SH5001 IMU传感器
- ESP32S3开发板
- USB连接线
- Windows系统 (COM6串口)

### 软件要求
- Python 3.7+
- 依赖库:
  - numpy
  - matplotlib
  - pyserial
  - imufusion
  - scipy

## 🚀 快速开始

### 1. 自动安装和运行
```bash
python setup_and_run.py
```
这个脚本会自动:
- 检查Python版本
- 安装所有依赖库
- 编译安装Imufusion库
- 检查串口状态
- 启动可视化程序

### 2. 手动安装依赖
```bash
# 安装基础依赖
pip install numpy matplotlib pyserial scipy

# 安装Imufusion (从本地源码)
cd ..
pip install -e .

# 或从PyPI安装
pip install imufusion
```

### 3. 运行程序

#### 简化版本 (推荐新手)
```bash
python sh5001_simple_3d.py
```

#### 完整版本 (包含Imufusion滤波)
```bash
python sh5001_fusion_3d.py
```

#### 基础版本
```bash
python sh5001_realtime_3d.py
```

## 📊 数据格式

### 输入格式
ESP32S3通过串口输出的四元数格式:
```
-0.0227,0.8463,-0.5212,-0.1077
```

### 数据顺序
假设四元数顺序为: `w, x, y, z`
- w: 标量部分
- x, y, z: 向量部分

如果您的数据顺序不同，请修改代码中的解析函数。

## 🔧 配置说明

### 串口配置
默认配置:
- 端口: COM6
- 波特率: 115200
- 超时: 1秒

修改串口设置:
```python
visualizer = SH5001Simple3D(port='COM7', baudrate=9600)
```

### 可视化配置
- 数据历史长度: 200-1000个数据点
- 更新频率: 20Hz (50ms间隔)
- 3D坐标轴范围: [-1.5, 1.5]

## 📈 功能说明

### 1. 简化版本 (sh5001_simple_3d.py)
- **3D姿态显示**: 实时显示传感器姿态
- **四元数曲线**: 四个分量的时间序列
- **欧拉角曲线**: Roll, Pitch, Yaw角度变化
- **状态监控**: 连接状态、数据统计

### 2. 完整版本 (sh5001_fusion_3d.py)
- **原始vs滤波对比**: 同时显示原始和滤波后的姿态
- **Imufusion滤波**: 使用AHRS算法进行数据滤波
- **多图表对比**: 原始和滤波数据的详细对比
- **滤波效果分析**: 四元数模长稳定性分析

### 3. 基础版本 (sh5001_realtime_3d.py)
- **多窗口显示**: 四个独立的图表窗口
- **详细数据分析**: 更多的数据处理和显示选项

## 🛠️ 故障排除

### 常见问题

#### 1. 串口连接失败
```
❌ 串口连接失败: [Errno 2] could not open port 'COM6'
```
**解决方案:**
- 检查ESP32S3是否正确连接
- 确认串口号是否为COM6
- 检查其他程序是否占用串口
- 尝试重新插拔USB线

#### 2. 数据解析错误
```
解析错误: could not convert string to float
```
**解决方案:**
- 检查ESP32S3输出的数据格式
- 确认数据是否为逗号分隔的四个浮点数
- 检查是否有额外的字符或换行符

#### 3. Imufusion安装失败
```
❌ Imufusion 安装失败
```
**解决方案:**
- 确保安装了Visual Studio Build Tools (Windows)
- 尝试使用conda安装: `conda install -c conda-forge imufusion`
- 手动编译安装

#### 4. 图形显示问题
**解决方案:**
- 更新matplotlib: `pip install --upgrade matplotlib`
- 检查显卡驱动
- 尝试不同的matplotlib后端

### 调试模式
在代码中添加调试信息:
```python
# 在parse_quaternion函数中添加
print(f"原始数据: {line}")
print(f"解析结果: {quat}")
```

## 📝 自定义开发

### 修改数据格式
如果您的四元数顺序不同，修改`parse_quaternion`函数:
```python
def parse_quaternion(self, line):
    values = [float(x.strip()) for x in line.split(',')]
    # 如果顺序是 x,y,z,w，则重新排列
    quat = np.array([values[3], values[0], values[1], values[2]])
    return quat
```

### 添加新的可视化
```python
def draw_custom_plot(self):
    # 添加自定义图表
    pass
```

### 修改滤波参数
```python
# 在Imufusion AHRS中修改参数
self.ahrs.settings.gain = 0.5  # 调整增益
```

## 📚 参考资料

- [Imufusion库文档](https://github.com/xioTechnologies/Fusion)
- [四元数数学原理](https://en.wikipedia.org/wiki/Quaternion)
- [ESP32S3开发指南](https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/)
- [SH5001传感器手册](https://www.senodia.com/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目遵循MIT许可证。

## 📞 支持

如果您遇到问题，请:
1. 检查本README的故障排除部分
2. 查看代码中的注释和文档
3. 提交Issue描述具体问题

---

**祝您使用愉快！** 🎉
