{"ACCEL": [-0.080688, 0.644165, -0.575317], "GYRO": [-2.462891, -17.099609, 17.212891], "Time": 92511}
{"ACCEL": [-0.161499, 0.655151, -0.720215], "GYRO": [-63.832031, -23.810547, 20.720703], "Time": 92529}
{"ACCEL": [-0.192383, 0.690308, -0.772705], "GYRO": [-41.083984, -57.812500, -1.490234], "Time": 92547}
{"ACCEL": [-0.274170, 0.715454, -0.664917], "GYRO": [-64.000000, -64.000000, -13.824219], "Time": 92565}
{"ACCEL": [-0.238892, 0.782715, -0.828613], "GYRO": [-64.000000, -64.000000, -14.652344], "Time": 92583}
{"ACCEL": [-0.307129, 0.713135, -0.524658], "GYRO": [-64.000000, -64.000000, -0.726562], "Time": 92601}
{"ACCEL": [-0.262939, 0.669800, -0.708252], "GYRO": [-12.789062, -64.000000, 44.087891], "Time": 92619}
{"ACCEL": [-0.281860, 0.499634, -0.735352], "GYRO": [62.623047, -64.000000, -64.000000], "Time": 92637}
{"ACCEL": [-0.237793, 0.601196, -0.559326], "GYRO": [-64.000000, -64.000000, 14.431641], "Time": 92655}
{"ACCEL": [-0.416138, 0.887695, -0.313110], "GYRO": [-64.000000, -50.160156, -8.068359], "Time": 92673}
{"ACCEL": [-0.364258, 0.815674, -0.482056], "GYRO": [-59.324219, -36.505859, 16.212891], "Time": 92691}
{"ACCEL": [-0.352295, 0.729370, -0.510254], "GYRO": [-34.933594, -1.251953, 32.501953], "Time": 92708}
{"ACCEL": [-0.318115, 0.773926, -0.268433], "GYRO": [-43.156250, 59.349609, 54.396484], "Time": 92726}
{"ACCEL": [-0.244385, 0.834839, -0.127563], "GYRO": [-64.000000, 63.998047, 63.998047], "Time": 92744}
{"ACCEL": [-0.316040, 0.941406, -0.363525], "GYRO": [-64.000000, 63.998047, 63.998047], "Time": 92762}
{"ACCEL": [-0.227783, 0.705811, -0.765259], "GYRO": [-28.658203, 63.998047, 63.998047], "Time": 92780}
{"ACCEL": [-0.130371, 1.019409, -0.498169], "GYRO": [-64.000000, 63.998047, 63.998047], "Time": 92798}
{"ACCEL": [-0.285400, 1.135376, -0.889771], "GYRO": [63.998047, 1.410156, 53.693359], "Time": 92816}
{"ACCEL": [-0.185303, 1.056030, -0.257812], "GYRO": [63.998047, 63.998047, 26.345703], "Time": 92834}
{"ACCEL": [-0.182373, 0.952148, -0.463257], "GYRO": [63.998047, 63.998047, 9.476562], "Time": 92852}
{"ACCEL": [-0.076294, 0.815430, -0.666382], "GYRO": [63.998047, 63.998047, -19.593750], "Time": 92870}
{"ACCEL": [-0.095825, 0.791870, -0.638184], "GYRO": [63.998047, 63.998047, -12.970703], "Time": 92888}
{"ACCEL": [-0.060303, 0.717773, -0.664673], "GYRO": [63.998047, 63.998047, -6.728516], "Time": 92905}
{"ACCEL": [-0.032715, 0.602173, -0.788208], "GYRO": [63.998047, 59.201172, 20.816406], "Time": 92923}
{"ACCEL": [-0.035034, 0.619141, -0.820801], "GYRO": [63.998047, 29.640625, 32.458984], "Time": 92941}
{"ACCEL": [-0.039551, 0.605591, -0.806030], "GYRO": [58.572266, 18.117188, 25.683594], "Time": 92959}
{"ACCEL": [0.011597, 0.640625, -0.772583], "GYRO": [52.859375, 26.505859, 8.023438], "Time": 92977}
{"ACCEL": [0.015381, 0.604126, -0.766113], "GYRO": [63.998047, 40.589844, 12.832031], "Time": 92995}
{"ACCEL": [0.014526, 0.563110, -0.829956], "GYRO": [2.371094, 33.248047, 4.558594], "Time": 93013}
{"ACCEL": [0.022949, 0.547485, -0.843506], "GYRO": [0.083984, 13.939453, 19.123047], "Time": 93031}
{"ACCEL": [0.041382, 0.580688, -0.835205], "GYRO": [63.257812, 13.230469, 1.492188], "Time": 93049}
{"ACCEL": [-0.005005, 0.541870, -0.793091], "GYRO": [19.146484, 12.369141, 8.888672], "Time": 93067}
{"ACCEL": [0.031250, 0.489746, -0.834839], "GYRO": [22.539062, 12.634766, 16.650391], "Time": 93085}
{"ACCEL": [0.049805, 0.542847, -0.852539], "GYRO": [63.025391, 3.744141, 4.048828], "Time": 93103}
{"ACCEL": [0.009644, 0.531982, -0.825806], "GYRO": [61.765625, 13.910156, -5.843750], "Time": 93120}
{"ACCEL": [0.069092, 0.499878, -0.844238], "GYRO": [63.998047, 11.662109, -7.673828], "Time": 93138}
{"ACCEL": [0.063721, 0.474487, -0.848633], "GYRO": [63.998047, 10.890625, 16.029297], "Time": 93156}
{"ACCEL": [0.083618, 0.404663, -0.893677], "GYRO": [63.998047, -11.634766, -5.472656], "Time": 93174}
{"ACCEL": [0.009399, 0.290894, -0.943115], "GYRO": [63.998047, 13.136719, 25.279297], "Time": 93192}
{"ACCEL": [0.112915, 0.416138, -0.835938], "GYRO": [63.998047, 14.390625, -18.968750], "Time": 93210}
{"ACCEL": [0.058472, 0.160645, -0.900879], "GYRO": [63.998047, 5.998047, 21.416016], "Time": 93228}
{"ACCEL": [0.108154, 0.244873, -0.880493], "GYRO": [46.140625, 6.880859, 30.544922], "Time": 93246}
{"ACCEL": [0.066406, 0.173584, -0.897339], "GYRO": [63.804688, 0.996094, 29.460938], "Time": 93264}
{"ACCEL": [0.061035, 0.173828, -0.897095], "GYRO": [22.148438, 16.658203, 20.675781], "Time": 93282}
{"ACCEL": [0.085449, 0.319092, -0.883667], "GYRO": [9.798828, 13.056641, 5.679688], "Time": 93300}
{"ACCEL": [0.084839, 0.212280, -0.909912], "GYRO": [-3.888672, 11.390625, 17.494141], "Time": 93318}
{"ACCEL": [0.086670, 0.115723, -0.909790], "GYRO": [9.394531, 15.835938, 20.269531], "Time": 93335}
{"ACCEL": [0.092407, 0.165649, -0.909912], "GYRO": [1.416016, 10.755859, 35.595703], "Time": 93353}
{"ACCEL": [0.100952, 0.180054, -0.897461], "GYRO": [-28.222656, 8.880859, 22.996094], "Time": 93371}
{"ACCEL": [0.062134, 0.170898, -0.819580], "GYRO": [-43.068359, 60.207031, 4.802734], "Time": 93389}
{"ACCEL": [0.120728, 0.222778, -0.954956], "GYRO": [11.458984, 59.773438, 25.255859], "Time": 93407}
{"ACCEL": [0.166626, 0.164307, -0.929932], "GYRO": [22.023438, 49.117188, 28.285156], "Time": 93425}
{"ACCEL": [0.125854, 0.248657, -0.893677], "GYRO": [20.820312, 38.931641, 10.376953], "Time": 93443}
{"ACCEL": [0.167603, 0.185547, -0.898926], "GYRO": [-47.408203, 49.007812, 24.585938], "Time": 93461}
{"ACCEL": [0.198853, 0.133423, -0.904419], "GYRO": [-22.679688, 0.609375, 42.386719], "Time": 93479}
{"ACCEL": [0.158813, 0.233887, -0.955933], "GYRO": [-4.585938, 3.955078, 33.562500], "Time": 93497}
{"ACCEL": [0.156616, 0.194824, -0.880981], "GYRO": [-40.060547, -8.236328, 22.302734], "Time": 93515}
{"ACCEL": [0.179443, 0.230591, -0.875000], "GYRO": [-64.000000, -4.904297, 23.074219], "Time": 93532}
{"ACCEL": [0.151733, 0.244629, -0.913574], "GYRO": [-64.000000, -0.562500, 22.404297], "Time": 93550}
{"ACCEL": [0.168091, 0.257202, -0.902588], "GYRO": [-7.058594, -0.089844, 13.873047], "Time": 93568}
{"ACCEL": [0.179321, 0.315796, -0.890259], "GYRO": [-59.830078, -29.300781, 20.353516], "Time": 93586}
{"ACCEL": [0.125244, 0.302246, -0.891724], "GYRO": [-55.542969, -19.097656, 4.355469], "Time": 93604}
{"ACCEL": [0.149536, 0.313843, -0.890259], "GYRO": [-62.123047, -24.664062, -4.433594], "Time": 93622}
{"ACCEL": [0.155762, 0.290527, -0.892334], "GYRO": [-37.009766, -12.900391, 2.443359], "Time": 93640}
{"ACCEL": [0.145874, 0.324341, -0.890625], "GYRO": [-15.904297, -4.175781, -1.798828], "Time": 93658}
{"ACCEL": [0.142944, 0.323242, -0.892334], "GYRO": [-24.277344, -6.048828, -4.408203], "Time": 93676}
{"ACCEL": [0.143311, 0.315308, -0.888062], "GYRO": [-32.876953, -11.189453, -1.400391], "Time": 93694}
{"ACCEL": [0.139648, 0.300049, -0.895142], "GYRO": [-46.609375, -13.931641, 14.453125], "Time": 93712}
{"ACCEL": [0.125977, 0.306885, -0.895386], "GYRO": [21.638672, 6.320312, 3.070312], "Time": 93730}
{"ACCEL": [0.124146, 0.357300, -0.885010], "GYRO": [63.998047, 43.037109, -0.708984], "Time": 93747}
{"ACCEL": [0.099854, 0.545898, -0.871338], "GYRO": [63.998047, 38.691406, -20.283203], "Time": 93765}
{"ACCEL": [0.199707, 0.196533, -0.890991], "GYRO": [51.580078, 10.375000, -11.080078], "Time": 93783}
{"ACCEL": [0.176758, 0.259033, -0.903687], "GYRO": [46.568359, 5.330078, 0.322266], "Time": 93801}
{"ACCEL": [0.179077, 0.230835, -0.900024], "GYRO": [63.998047, 5.548828, 4.582031], "Time": 93819}
{"ACCEL": [0.194824, 0.210449, -0.923950], "GYRO": [63.998047, 10.525391, 23.261719], "Time": 93837}
{"ACCEL": [0.175293, 0.202148, -0.918457], "GYRO": [63.998047, -0.099609, 0.490234], "Time": 93855}
{"ACCEL": [0.152222, 0.133667, -0.896851], "GYRO": [63.998047, -0.017578, 8.755859], "Time": 93873}
{"ACCEL": [0.177979, 0.065796, -0.857788], "GYRO": [19.134766, -3.320312, 1.302734], "Time": 93891}
{"ACCEL": [0.158325, 0.095825, -0.891235], "GYRO": [-1.093750, 3.052734, -9.599609], "Time": 93909}
{"ACCEL": [0.167114, 0.081177, -0.888184], "GYRO": [0.304688, 1.742188, -4.564453], "Time": 93927}
{"ACCEL": [0.190552, 0.090576, -0.935181], "GYRO": [23.410156, -3.423828, -5.404297], "Time": 93945}
{"ACCEL": [0.175171, 0.076904, -0.892456], "GYRO": [-7.755859, 3.453125, -2.287109], "Time": 93962}
{"ACCEL": [0.168213, 0.068481, -0.880005], "GYRO": [-16.935547, 5.150391, -3.167969], "Time": 93980}
{"ACCEL": [0.171387, 0.080688, -0.913086], "GYRO": [21.992188, -0.980469, 0.050781], "Time": 93998}
{"ACCEL": [0.179077, 0.083618, -0.909912], "GYRO": [14.917969, -3.650391, 2.433594], "Time": 94016}
{"ACCEL": [0.182495, 0.097290, -0.969482], "GYRO": [63.998047, -14.759766, -7.097656], "Time": 94034}
{"ACCEL": [0.163452, 0.072021, -0.917358], "GYRO": [52.740234, -4.916016, -5.152344], "Time": 94052}
{"ACCEL": [0.165405, 0.007080, -0.820068], "GYRO": [10.154297, 0.136719, -4.423828], "Time": 94070}
{"ACCEL": [0.172485, 0.016968, -0.894775], "GYRO": [-7.326172, 2.046875, -1.531250], "Time": 94088}
{"ACCEL": [0.166382, 0.018311, -0.895508], "GYRO": [-17.226562, 2.494141, -3.195312], "Time": 94106}
{"ACCEL": [0.169189, 0.033203, -0.945068], "GYRO": [18.871094, 1.292969, -2.181641], "Time": 94124}
{"ACCEL": [0.163330, 0.009766, -0.873901], "GYRO": [-4.947266, 1.582031, -0.482422], "Time": 94142}
{"ACCEL": [0.167725, 0.010010, -0.893555], "GYRO": [-1.761719, 1.775391, -0.445312], "Time": 94159}
{"ACCEL": [0.165161, 0.013062, -0.903564], "GYRO": [-5.357422, 1.570312, -0.580078], "Time": 94177}
{"ACCEL": [0.165527, 0.011963, -0.902710], "GYRO": [0.068359, 0.800781, -0.902344], "Time": 94195}
{"ACCEL": [0.164062, 0.015259, -0.916748], "GYRO": [63.998047, -4.419922, -15.449219], "Time": 94213}
{"ACCEL": [0.146606, 0.003174, -0.928345], "GYRO": [36.445312, -0.775391, -2.443359], "Time": 94231}
{"ACCEL": [0.190918, -0.022339, -0.851196], "GYRO": [-7.529297, 2.839844, 1.023438], "Time": 94249}
{"ACCEL": [0.160156, -0.012695, -0.887695], "GYRO": [-12.939453, 3.148438, 1.462891], "Time": 94267}
{"ACCEL": [0.163452, -0.006226, -0.911621], "GYRO": [-1.746094, 1.949219, -1.070312], "Time": 94285}
{"ACCEL": [0.165039, -0.014160, -0.892578], "GYRO": [-9.359375, 2.203125, -0.744141], "Time": 94303}
{"ACCEL": [0.165283, -0.014160, -0.899170], "GYRO": [-6.406250, 2.363281, -0.214844], "Time": 94321}
{"ACCEL": [0.165161, -0.010498, -0.899902], "GYRO": [-6.607422, 1.992188, 0.082031], "Time": 94339}
{"ACCEL": [0.165161, -0.010742, -0.899048], "GYRO": [-9.289062, 2.451172, -0.398438], "Time": 94357}
{"ACCEL": [0.165161, -0.010498, -0.898193], "GYRO": [-17.630859, 2.917969, 0.484375], "Time": 94374}
{"ACCEL": [0.165161, -0.004883, -0.899902], "GYRO": [-14.248047, 2.718750, 0.693359], "Time": 94392}
{"ACCEL": [0.165161, 0.003418, -0.900879], "GYRO": [-10.382812, 2.525391, 1.357422], "Time": 94410}
{"ACCEL": [0.165161, 0.006226, -0.900635], "GYRO": [-8.472656, 2.451172, 1.558594], "Time": 94428}
{"ACCEL": [0.165161, 0.008667, -0.900146], "GYRO": [-9.757812, 2.417969, -0.406250], "Time": 94446}
{"ACCEL": [0.165161, 0.008789, -0.900391], "GYRO": [-8.623047, 2.242188, -0.076172], "Time": 94464}
{"ACCEL": [0.165161, 0.009888, -0.900269], "GYRO": [-11.132812, 2.390625, -0.105469], "Time": 94482}
{"ACCEL": [0.165161, 0.011108, -0.900391], "GYRO": [-10.677734, 2.423828, 0.236328], "Time": 94500}
{"ACCEL": [0.165161, 0.017822, -0.900269], "GYRO": [-9.699219, 2.597656, 0.369141], "Time": 94518}
{"ACCEL": [0.165161, 0.022217, -0.902832], "GYRO": [-1.767578, 1.250000, 0.001953], "Time": 94536}
{"ACCEL": [0.165161, 0.020752, -0.899658], "GYRO": [-6.972656, 1.888672, 0.326172], "Time": 94554}
{"ACCEL": [0.165161, 0.021362, -0.900024], "GYRO": [-6.617188, 1.732422, -0.072266], "Time": 94571}
{"ACCEL": [0.165161, 0.024170, -0.900513], "GYRO": [-5.277344, 1.867188, -0.140625], "Time": 94589}
{"ACCEL": [0.165161, 0.024170, -0.900269], "GYRO": [-3.701172, 1.761719, -0.115234], "Time": 94607}
{"ACCEL": [0.165161, 0.026611, -0.901978], "GYRO": [-3.177734, 1.792969, -0.404297], "Time": 94625}
{"ACCEL": [0.165161, 0.025513, -0.900146], "GYRO": [-1.595703, 1.548828, -0.117188], "Time": 94643}
{"ACCEL": [0.165161, 0.026733, -0.900269], "GYRO": [-3.216797, 1.791016, -0.072266], "Time": 94661}
{"ACCEL": [0.165405, 0.025146, -0.900391], "GYRO": [-5.046875, 1.666016, -0.248047], "Time": 94679}
{"ACCEL": [0.165527, 0.025513, -0.900391], "GYRO": [-6.855469, 1.798828, 0.123047], "Time": 94697}
{"ACCEL": [0.165039, 0.027710, -0.900391], "GYRO": [-2.523438, 1.488281, -0.150391], "Time": 94715}
{"ACCEL": [0.165283, 0.029175, -0.900635], "GYRO": [-1.361328, 1.318359, -0.087891], "Time": 94733}
{"ACCEL": [0.165161, 0.028442, -0.900757], "GYRO": [-2.937500, 1.390625, -0.486328], "Time": 94751}
{"ACCEL": [0.165161, 0.028809, -0.900146], "GYRO": [-3.919922, 1.376953, -0.445312], "Time": 94769}
{"ACCEL": [0.165161, 0.029175, -0.900391], "GYRO": [-4.984375, 1.554688, -0.562500], "Time": 94786}
{"ACCEL": [0.165161, 0.030273, -0.900391], "GYRO": [-3.812500, 1.691406, -0.392578], "Time": 94804}
{"ACCEL": [0.165161, 0.030273, -0.900269], "GYRO": [-7.603516, 2.412109, -0.482422], "Time": 94822}
{"ACCEL": [0.165771, 0.031250, -0.900635], "GYRO": [-6.673828, 2.089844, -0.628906], "Time": 94840}
{"ACCEL": [0.165161, 0.033325, -0.899292], "GYRO": [-10.083984, 2.748047, -0.189453], "Time": 94858}
{"ACCEL": [0.165161, 0.038086, -0.902100], "GYRO": [-5.000000, 2.177734, -0.476562], "Time": 94876}
{"ACCEL": [0.165283, 0.039673, -0.901123], "GYRO": [-3.603516, 1.792969, -0.240234], "Time": 94894}
{"ACCEL": [0.165161, 0.038940, -0.900146], "GYRO": [-1.531250, 1.419922, -0.494141], "Time": 94912}
{"ACCEL": [0.165161, 0.039185, -0.900391], "GYRO": [-2.453125, 1.515625, -0.457031], "Time": 94930}
{"ACCEL": [0.165161, 0.037964, -0.900269], "GYRO": [-4.513672, 1.687500, -0.533203], "Time": 94948}
{"ACCEL": [0.165649, 0.038208, -0.900391], "GYRO": [-4.382812, 1.562500, -0.457031], "Time": 94966}
{"ACCEL": [0.165283, 0.039795, -0.900391], "GYRO": [-3.839844, 1.767578, -0.529297], "Time": 94984}
{"ACCEL": [0.165771, 0.041870, -0.901245], "GYRO": [-0.457031, 0.849609, -0.318359], "Time": 95001}
{"ACCEL": [0.165527, 0.041626, -0.900146], "GYRO": [-1.083984, 1.302734, -0.117188], "Time": 95019}
{"ACCEL": [0.165649, 0.040527, -0.900391], "GYRO": [-2.617188, 1.267578, -0.591797], "Time": 95037}
{"ACCEL": [0.165771, 0.041504, -0.901123], "GYRO": [-3.935547, 1.753906, -0.406250], "Time": 95055}
{"ACCEL": [0.165039, 0.041260, -0.900269], "GYRO": [-4.099609, 1.833984, -0.476562], "Time": 95073}
{"ACCEL": [0.165283, 0.042847, -0.900391], "GYRO": [-1.626953, 1.181641, -0.425781], "Time": 95091}
{"ACCEL": [0.165161, 0.043823, -0.900269], "GYRO": [-2.019531, 1.382812, -0.513672], "Time": 95109}
{"ACCEL": [0.165161, 0.042847, -0.900391], "GYRO": [-3.560547, 1.800781, -0.455078], "Time": 95127}
{"ACCEL": [0.165649, 0.043579, -0.900391], "GYRO": [-1.427734, 1.072266, -0.160156], "Time": 95145}
{"ACCEL": [0.165649, 0.042236, -0.900269], "GYRO": [-3.886719, 1.667969, 0.001953], "Time": 95163}
{"ACCEL": [0.165283, 0.042969, -0.900391], "GYRO": [-0.244141, 1.148438, -0.033203], "Time": 95181}
{"ACCEL": [0.165161, 0.043091, -0.900269], "GYRO": [-0.033203, 1.058594, -0.134766], "Time": 95199}
{"ACCEL": [0.165771, 0.041748, -0.900391], "GYRO": [-0.886719, 1.498047, -0.343750], "Time": 95216}
{"ACCEL": [0.165161, 0.041992, -0.900269], "GYRO": [-4.328125, 1.572266, -0.531250], "Time": 95234}
{"ACCEL": [0.165161, 0.044067, -0.900391], "GYRO": [-3.773438, 1.822266, -0.406250], "Time": 95252}
{"ACCEL": [0.165161, 0.043701, -0.901245], "GYRO": [-1.976562, 1.492188, -0.238281], "Time": 95270}
{"ACCEL": [0.165283, 0.042969, -0.900269], "GYRO": [-1.490234, 1.507812, -0.312500], "Time": 95288}
{"ACCEL": [0.165771, 0.043091, -0.900269], "GYRO": [-2.634766, 1.576172, -0.460938], "Time": 95306}
{"ACCEL": [0.166138, 0.043701, -0.900391], "GYRO": [-2.023438, 1.375000, -0.287109], "Time": 95324}
{"ACCEL": [0.165161, 0.043457, -0.900391], "GYRO": [-0.783203, 1.085938, -0.212891], "Time": 95342}
{"ACCEL": [0.165161, 0.043457, -0.900269], "GYRO": [-1.482422, 1.296875, -0.238281], "Time": 95360}
{"ACCEL": [0.165527, 0.042847, -0.900391], "GYRO": [-2.666016, 1.537109, -0.595703], "Time": 95378}
{"ACCEL": [0.165649, 0.044678, -0.900391], "GYRO": [-1.697266, 1.433594, -0.345703], "Time": 95396}
{"ACCEL": [0.165405, 0.044556, -0.900269], "GYRO": [-0.109375, 0.966797, -0.128906], "Time": 95413}
{"ACCEL": [0.165649, 0.043335, -0.900269], "GYRO": [-1.728516, 1.691406, -0.310547], "Time": 95431}
{"ACCEL": [0.165283, 0.042480, -0.900391], "GYRO": [-1.041016, 1.167969, -0.113281], "Time": 95449}
{"ACCEL": [0.165161, 0.041748, -0.900269], "GYRO": [-2.314453, 1.640625, -0.455078], "Time": 95467}
{"ACCEL": [0.165161, 0.041382, -0.900391], "GYRO": [-2.101562, 1.957031, -0.271484], "Time": 95485}
{"ACCEL": [0.165161, 0.041870, -0.901855], "GYRO": [-0.648438, 1.072266, -0.082031], "Time": 95503}
{"ACCEL": [0.165649, 0.041992, -0.900513], "GYRO": [-3.447266, 1.427734, -0.351562], "Time": 95521}
{"ACCEL": [0.165283, 0.043091, -0.900269], "GYRO": [-2.443359, 1.287109, -0.474609], "Time": 95539}
{"ACCEL": [0.165161, 0.045288, -0.900391], "GYRO": [-1.533203, 1.193359, -0.283203], "Time": 95557}
{"ACCEL": [0.165161, 0.043945, -0.900391], "GYRO": [-0.650391, 1.705078, -0.183594], "Time": 95575}
{"ACCEL": [0.165161, 0.041748, -0.900269], "GYRO": [-2.007812, 1.332031, -0.335938], "Time": 95593}
{"ACCEL": [0.165527, 0.040405, -0.900391], "GYRO": [-2.771484, 1.435547, -0.556641], "Time": 95611}
{"ACCEL": [0.165405, 0.041504, -0.900391], "GYRO": [-1.630859, 1.476562, -0.277344], "Time": 95628}
{"ACCEL": [0.165161, 0.042480, -0.900391], "GYRO": [-0.552734, 1.113281, -0.166016], "Time": 95646}
{"ACCEL": [0.165161, 0.043091, -0.900269], "GYRO": [0.343750, 1.140625, -0.191406], "Time": 95664}
{"ACCEL": [0.165161, 0.041626, -0.900269], "GYRO": [-3.060547, 1.386719, -0.134766], "Time": 95682}
{"ACCEL": [0.165283, 0.040283, -0.900391], "GYRO": [-3.101562, 1.240234, -0.675781], "Time": 95700}
{"ACCEL": [0.165649, 0.041382, -0.900391], "GYRO": [-1.205078, 1.458984, -0.416016], "Time": 95718}
{"ACCEL": [0.165039, 0.041138, -0.900269], "GYRO": [-1.660156, 1.136719, -0.347656], "Time": 95736}
{"ACCEL": [0.165161, 0.041138, -0.900269], "GYRO": [-3.324219, 1.556641, -0.515625], "Time": 95754}
{"ACCEL": [0.165161, 0.043213, -0.900391], "GYRO": [-2.083984, 1.267578, -0.085938], "Time": 95772}
{"ACCEL": [0.165161, 0.043213, -0.900269], "GYRO": [-0.218750, 1.142578, -0.287109], "Time": 95790}
{"ACCEL": [0.166138, 0.041016, -0.900269], "GYRO": [-2.009766, 1.164062, -0.394531], "Time": 95808}
{"ACCEL": [0.166748, 0.040283, -0.900391], "GYRO": [-3.466797, 1.513672, -0.580078], "Time": 95825}
{"ACCEL": [0.165161, 0.041382, -0.900391], "GYRO": [-3.236328, 1.593750, -0.457031], "Time": 95843}
{"ACCEL": [0.165161, 0.042847, -0.901123], "GYRO": [-0.886719, 1.376953, -0.259766], "Time": 95861}
{"ACCEL": [0.165283, 0.043701, -0.900391], "GYRO": [-0.166016, 1.406250, -0.171875], "Time": 95879}
{"ACCEL": [0.165161, 0.042847, -0.900269], "GYRO": [-1.683594, 1.306641, -0.466797], "Time": 95897}
{"ACCEL": [0.166138, 0.042358, -0.900391], "GYRO": [-4.539062, 1.763672, -0.794922], "Time": 95915}
{"ACCEL": [0.165405, 0.041504, -0.900391], "GYRO": [-2.117188, 1.148438, -0.414062], "Time": 95933}
{"ACCEL": [0.165039, 0.042236, -0.900269], "GYRO": [-0.390625, 1.367188, -0.267578], "Time": 95951}
{"ACCEL": [0.165161, 0.039917, -0.900391], "GYRO": [-0.787109, 1.123047, -0.220703], "Time": 95969}
{"ACCEL": [0.165161, 0.040894, -0.900269], "GYRO": [-3.359375, 1.513672, -0.613281], "Time": 95987}
{"ACCEL": [0.165771, 0.042236, -0.900391], "GYRO": [-3.804688, 1.683594, -0.453125], "Time": 96005}
{"ACCEL": [0.166626, 0.043579, -0.900757], "GYRO": [-1.318359, 1.185547, -0.302734], "Time": 96023}
{"ACCEL": [0.165161, 0.043701, -0.900879], "GYRO": [-1.080078, 1.316406, -0.158203], "Time": 96040}
{"ACCEL": [0.165161, 0.042114, -0.900269], "GYRO": [-0.466797, 0.966797, -0.152344], "Time": 96058}
{"ACCEL": [0.165161, 0.040405, -0.900391], "GYRO": [-2.632812, 1.669922, -0.308594], "Time": 96076}
{"ACCEL": [0.165649, 0.040161, -0.900391], "GYRO": [-2.812500, 1.576172, -0.476562], "Time": 96094}
{"ACCEL": [0.165405, 0.041626, -0.900391], "GYRO": [-3.015625, 1.501953, -0.287109], "Time": 96112}
{"ACCEL": [0.165161, 0.042114, -0.900391], "GYRO": [-1.345703, 1.253906, -0.414062], "Time": 96130}
{"ACCEL": [0.166260, 0.043091, -0.900391], "GYRO": [0.113281, 1.078125, -0.167969], "Time": 96148}
{"ACCEL": [0.165649, 0.041870, -0.900269], "GYRO": [-1.531250, 1.347656, -0.292969], "Time": 96166}
{"ACCEL": [0.165283, 0.040649, -0.900391], "GYRO": [-2.363281, 1.193359, -0.396484], "Time": 96184}
{"ACCEL": [0.165161, 0.041260, -0.900391], "GYRO": [-0.744141, 1.408203, -0.333984], "Time": 96202}
{"ACCEL": [0.165161, 0.040894, -0.900269], "GYRO": [-1.824219, 1.126953, -0.035156], "Time": 96220}
{"ACCEL": [0.165161, 0.040405, -0.900391], "GYRO": [-2.845703, 1.316406, -0.296875], "Time": 96238}
{"ACCEL": [0.165161, 0.042114, -0.900391], "GYRO": [-2.673828, 1.375000, -0.384766], "Time": 96511}
{"ACCEL": [0.165161, 0.043701, -0.900391], "GYRO": [-0.609375, 1.033203, -0.380859], "Time": 16776978}
{"ACCEL": [0.165405, 0.041992, -0.900269], "GYRO": [-0.962891, 0.994141, -0.330078], "Time": 96529}
{"ACCEL": [0.165527, 0.040771, -0.900269], "GYRO": [-3.148438, 1.392578, -0.453125], "Time": 16776996}
{"ACCEL": [0.165527, 0.040527, -0.900391], "GYRO": [-3.322266, 1.527344, -0.564453], "Time": 96547}
{"ACCEL": [0.165283, 0.040894, -0.900391], "GYRO": [-1.226562, 1.449219, -0.294922], "Time": 16777014}
{"ACCEL": [0.165161, 0.042236, -0.900269], "GYRO": [-0.589844, 1.138672, -0.316406], "Time": 96565}
{"ACCEL": [0.165161, 0.042847, -0.900269], "GYRO": [-2.716797, 1.464844, -0.378906], "Time": 16777032}
{"ACCEL": [0.165649, 0.041626, -0.900391], "GYRO": [-5.998047, 1.828125, -0.666016], "Time": 96583}
{"ACCEL": [0.165771, 0.043091, -0.900391], "GYRO": [-3.162109, 1.492188, -0.494141], "Time": 16777050}
{"ACCEL": [0.165649, 0.044312, -0.901123], "GYRO": [1.132812, 0.998047, 0.150391], "Time": 96601}
{"ACCEL": [0.165283, 0.042603, -0.900146], "GYRO": [-1.970703, 1.220703, -0.380859], "Time": 16777067}
{"ACCEL": [0.165161, 0.041992, -0.900391], "GYRO": [-4.009766, 1.609375, -0.623047], "Time": 96619}
{"ACCEL": [0.165771, 0.042480, -0.900391], "GYRO": [-3.394531, 1.605469, -0.289062], "Time": 16777085}
{"ACCEL": [0.165161, 0.044189, -0.900757], "GYRO": [-0.025391, 0.888672, -0.158203], "Time": 96637}
{"ACCEL": [0.165649, 0.044067, -0.900757], "GYRO": [-1.865234, 1.271484, -0.210938], "Time": 16777103}
{"ACCEL": [0.165283, 0.044067, -0.900146], "GYRO": [-3.326172, 1.466797, -0.544922], "Time": 96655}
{"ACCEL": [0.165161, 0.043457, -0.900391], "GYRO": [-1.843750, 1.570312, -0.169922], "Time": 16777121}
{"ACCEL": [0.165405, 0.044678, -0.900269], "GYRO": [-0.873047, 0.865234, -0.318359], "Time": 96673}
{"ACCEL": [0.165527, 0.042236, -0.900391], "GYRO": [-0.972656, 1.031250, -0.357422], "Time": 16777139}
{"ACCEL": [0.165039, 0.042603, -0.900269], "GYRO": [-3.041016, 1.648438, -0.335938], "Time": 96691}
{"ACCEL": [0.165161, 0.042969, -0.900391], "GYRO": [-1.136719, 1.232422, -0.294922], "Time": 16777157}
{"ACCEL": [0.165527, 0.043945, -0.900269], "GYRO": [1.691406, 0.882812, -0.269531], "Time": 96709}
{"ACCEL": [0.165405, 0.042114, -0.900269], "GYRO": [0.941406, 1.234375, -0.265625], "Time": 16777174}
{"ACCEL": [0.165161, 0.040894, -0.900269], "GYRO": [-1.318359, 1.781250, -0.326172], "Time": 96727}
{"ACCEL": [0.165649, 0.042725, -0.900391], "GYRO": [0.042969, 0.912109, -0.843750], "Time": 16777192}
{"ACCEL": [0.165405, 0.036133, -0.874878], "GYRO": [-64.000000, 9.789062, -0.863281], "Time": 96745}
{"ACCEL": [0.169678, 0.046265, -0.897583], "GYRO": [-29.669922, 6.044922, -1.666016], "Time": 16777210}
{"ACCEL": [0.178345, 0.048828, -0.902222], "GYRO": [-32.830078, 6.679688, -0.046875], "Time": 96763}
{"ACCEL": [0.175049, 0.073608, -0.913452], "GYRO": [-13.308594, 2.998047, -0.630859], "Time": 96775}
{"ACCEL": [0.175171, 0.087524, -0.935791], "GYRO": [22.136719, -4.085938, -5.199219], "Time": 96793}
{"ACCEL": [0.161499, 0.093140, -0.915649], "GYRO": [26.052734, -4.580078, -10.460938], "Time": 96811}
{"ACCEL": [0.167847, 0.061890, -0.892578], "GYRO": [9.990234, -1.693359, -9.246094], "Time": 96829}
{"ACCEL": [0.171265, 0.046509, -0.894165], "GYRO": [-2.105469, 1.419922, -4.597656], "Time": 96847}
{"ACCEL": [0.162720, 0.048828, -0.877075], "GYRO": [-9.699219, 3.638672, -2.289062], "Time": 96865}
{"ACCEL": [0.169067, 0.067261, -0.919312], "GYRO": [-9.697266, 4.408203, -3.654297], "Time": 96882}
{"ACCEL": [0.164917, 0.085449, -0.899658], "GYRO": [-18.070312, 5.296875, -13.326172], "Time": 96900}
{"ACCEL": [0.192139, 0.068726, -0.892578], "GYRO": [-33.314453, 7.876953, -1.593750], "Time": 96918}
{"ACCEL": [0.191162, 0.061890, -0.888672], "GYRO": [-36.046875, 8.140625, 4.312500], "Time": 96936}
{"ACCEL": [0.176147, 0.083374, -0.940063], "GYRO": [-13.531250, 0.669922, -1.011719], "Time": 96954}
{"ACCEL": [0.175171, 0.087769, -0.898438], "GYRO": [-11.236328, 2.500000, 1.703125], "Time": 96972}
{"ACCEL": [0.177246, 0.094727, -0.902466], "GYRO": [-9.988281, 1.728516, 1.039062], "Time": 96990}
{"ACCEL": [0.167358, 0.101562, -0.898804], "GYRO": [-20.964844, 1.582031, -5.728516], "Time": 97008}
{"ACCEL": [0.191406, 0.096313, -0.900024], "GYRO": [-28.134766, 2.062500, 10.314453], "Time": 97026}
{"ACCEL": [0.212402, 0.078491, -0.903809], "GYRO": [-28.166016, 1.152344, 1.525391], "Time": 97044}
{"ACCEL": [0.162476, 0.112183, -0.879272], "GYRO": [-64.000000, -1.091797, -3.384766], "Time": 97062}
{"ACCEL": [0.146362, 0.177979, -0.918091], "GYRO": [-32.689453, -4.332031, -1.312500], "Time": 97079}
{"ACCEL": [0.162598, 0.179443, -0.928955], "GYRO": [15.093750, -1.341797, -39.681641], "Time": 97097}
{"ACCEL": [0.166504, 0.182861, -0.908203], "GYRO": [-6.923828, -0.417969, -22.562500], "Time": 97115}
{"ACCEL": [0.179443, 0.122803, -0.895020], "GYRO": [-10.632812, -1.355469, -10.103516], "Time": 97133}
{"ACCEL": [0.178589, 0.102539, -0.906250], "GYRO": [-13.949219, -6.951172, 4.640625], "Time": 97151}
{"ACCEL": [0.192383, 0.113037, -0.897461], "GYRO": [-30.128906, -5.824219, -1.666016], "Time": 97169}
{"ACCEL": [0.204712, 0.183838, -0.895264], "GYRO": [-24.826172, -4.373047, -2.939453], "Time": 97187}
{"ACCEL": [0.208740, 0.133179, -0.889648], "GYRO": [-21.558594, -9.689453, -0.396484], "Time": 97205}
{"ACCEL": [0.189453, 0.198486, -0.913574], "GYRO": [63.998047, 51.353516, 6.699219], "Time": 97223}
{"ACCEL": [0.169678, 0.227905, -0.876709], "GYRO": [-54.992188, -2.916016, -12.179688], "Time": 97241}
{"ACCEL": [0.182007, 0.238037, -0.908813], "GYRO": [-10.908203, 4.630859, -16.972656], "Time": 97259}
{"ACCEL": [0.195312, 0.250854, -0.895020], "GYRO": [-15.472656, 5.589844, 1.958984], "Time": 97277}
{"ACCEL": [0.156616, 0.202393, -0.906494], "GYRO": [-18.906250, -3.480469, -3.208984], "Time": 97294}
{"ACCEL": [0.154663, 0.271851, -0.881714], "GYRO": [-27.181641, 1.994141, -6.943359], "Time": 97312}
{"ACCEL": [0.171021, 0.301514, -0.855713], "GYRO": [-64.000000, -10.488281, 31.962891], "Time": 97330}
{"ACCEL": [0.186157, 0.156006, -0.896362], "GYRO": [-64.000000, -64.000000, 8.535156], "Time": 97348}
{"ACCEL": [0.037598, 0.342651, -0.933105], "GYRO": [44.773438, 20.884766, -0.208984], "Time": 97366}
{"ACCEL": [0.092651, 0.378418, -0.871582], "GYRO": [-30.996094, -7.228516, -4.583984], "Time": 97384}
{"ACCEL": [0.150513, 0.340454, -0.886475], "GYRO": [-5.460938, 1.222656, -0.619141], "Time": 97402}
{"ACCEL": [0.130249, 0.365601, -0.883789], "GYRO": [-10.710938, -0.480469, -1.554688], "Time": 97420}
{"ACCEL": [0.134155, 0.359741, -0.881958], "GYRO": [-0.898438, 2.769531, -0.025391], "Time": 97438}
{"ACCEL": [0.130127, 0.364868, -0.885620], "GYRO": [-7.869141, 0.664062, -1.076172], "Time": 97456}
{"ACCEL": [0.130737, 0.364136, -0.881470], "GYRO": [-4.355469, 1.765625, -0.820312], "Time": 97474}
{"ACCEL": [0.131836, 0.365112, -0.882935], "GYRO": [-4.738281, 1.826172, -0.789062], "Time": 97492}
{"ACCEL": [0.132812, 0.365112, -0.882324], "GYRO": [-3.078125, 2.224609, -0.498047], "Time": 97509}
{"ACCEL": [0.135132, 0.365112, -0.882812], "GYRO": [-4.740234, 1.373047, -0.343750], "Time": 97527}
{"ACCEL": [0.136719, 0.364502, -0.879150], "GYRO": [-7.345703, 0.638672, -0.742188], "Time": 97545}
{"ACCEL": [0.133423, 0.368286, -0.884399], "GYRO": [-3.814453, 1.666016, -0.373047], "Time": 97563}
{"ACCEL": [0.131836, 0.367432, -0.881958], "GYRO": [-4.005859, 1.935547, -0.503906], "Time": 97581}
{"ACCEL": [0.131714, 0.368652, -0.883545], "GYRO": [-3.898438, 1.728516, -0.562500], "Time": 97599}
{"ACCEL": [0.129150, 0.371460, -0.880737], "GYRO": [-3.902344, 1.771484, -0.541016], "Time": 97617}
{"ACCEL": [0.128906, 0.374023, -0.880005], "GYRO": [-4.105469, 1.941406, -0.833984], "Time": 97635}
{"ACCEL": [0.129761, 0.374512, -0.879639], "GYRO": [-4.298828, 1.720703, -0.601562], "Time": 97653}
{"ACCEL": [0.130127, 0.372681, -0.880371], "GYRO": [-3.904297, 1.744141, -0.691406], "Time": 97671}
{"ACCEL": [0.127686, 0.371338, -0.880371], "GYRO": [-3.818359, 1.966797, -0.626953], "Time": 97689}
{"ACCEL": [0.129272, 0.370483, -0.879395], "GYRO": [-3.490234, 1.968750, -0.511719], "Time": 97706}
{"ACCEL": [0.133545, 0.369019, -0.880737], "GYRO": [-3.734375, 1.777344, -0.539062], "Time": 97724}
{"ACCEL": [0.135254, 0.370361, -0.878052], "GYRO": [-3.826172, 1.443359, -0.433594], "Time": 97742}
{"ACCEL": [0.132446, 0.373291, -0.878662], "GYRO": [-3.595703, 1.875000, -0.212891], "Time": 97760}
{"ACCEL": [0.130737, 0.374146, -0.878784], "GYRO": [-3.650391, 1.998047, -0.539062], "Time": 97778}
{"ACCEL": [0.129395, 0.375854, -0.881958], "GYRO": [-3.873047, 1.716797, -0.562500], "Time": 97796}
{"ACCEL": [0.125488, 0.376953, -0.879517], "GYRO": [-3.853516, 1.638672, -0.439453], "Time": 97814}
{"ACCEL": [0.123779, 0.379150, -0.881104], "GYRO": [-3.451172, 2.031250, -0.552734], "Time": 97832}
{"ACCEL": [0.125732, 0.378540, -0.879883], "GYRO": [-2.996094, 2.457031, -0.398438], "Time": 97850}
{"ACCEL": [0.127563, 0.376831, -0.880005], "GYRO": [-3.431641, 2.333984, -0.365234], "Time": 97868}
{"ACCEL": [0.128906, 0.377197, -0.879395], "GYRO": [-3.146484, 2.000000, -0.410156], "Time": 97886}
{"ACCEL": [0.130493, 0.375244, -0.878418], "GYRO": [-3.146484, 2.160156, -0.570312], "Time": 97904}
{"ACCEL": [0.130371, 0.374146, -0.881348], "GYRO": [-3.126953, 1.873047, -0.417969], "Time": 97921}
{"ACCEL": [0.129150, 0.375122, -0.879028], "GYRO": [-3.328125, 2.128906, -0.269531], "Time": 97939}
{"ACCEL": [0.128052, 0.377075, -0.880127], "GYRO": [-3.416016, 1.984375, -0.583984], "Time": 97957}
{"ACCEL": [0.127686, 0.377563, -0.880615], "GYRO": [-3.601562, 1.787109, -0.587891], "Time": 97975}
{"ACCEL": [0.126953, 0.379639, -0.880615], "GYRO": [-3.341797, 2.126953, -0.625000], "Time": 97993}
{"ACCEL": [0.127563, 0.381958, -0.879028], "GYRO": [-3.509766, 2.105469, -0.330078], "Time": 98011}
{"ACCEL": [0.125244, 0.384766, -0.875977], "GYRO": [-2.648438, 2.109375, -0.488281], "Time": 98029}
{"ACCEL": [0.125732, 0.382446, -0.875610], "GYRO": [-2.826172, 2.023438, -0.445312], "Time": 98047}
{"ACCEL": [0.123901, 0.380371, -0.877441], "GYRO": [-3.105469, 1.986328, -0.656250], "Time": 98065}
{"ACCEL": [0.123535, 0.380859, -0.880249], "GYRO": [-2.894531, 2.082031, -0.337891], "Time": 98083}
{"ACCEL": [0.124023, 0.380493, -0.877075], "GYRO": [-3.044922, 2.046875, -0.394531], "Time": 98101}
{"ACCEL": [0.127686, 0.381470, -0.878906], "GYRO": [-2.763672, 2.140625, -0.457031], "Time": 98118}
{"ACCEL": [0.132690, 0.377319, -0.879517], "GYRO": [-3.218750, 1.839844, -0.371094], "Time": 98136}
{"ACCEL": [0.130493, 0.377075, -0.879761], "GYRO": [-3.154297, 1.880859, -0.550781], "Time": 98154}
{"ACCEL": [0.126465, 0.379761, -0.876587], "GYRO": [-2.896484, 2.113281, -0.533203], "Time": 98172}
{"ACCEL": [0.126587, 0.380737, -0.877441], "GYRO": [-2.822266, 1.939453, -0.386719], "Time": 98190}
{"ACCEL": [0.128174, 0.379028, -0.878296], "GYRO": [-3.779297, 1.582031, -0.453125], "Time": 98208}
{"ACCEL": [0.127808, 0.380981, -0.884155], "GYRO": [-3.494141, 1.705078, -0.662109], "Time": 98226}
{"ACCEL": [0.129883, 0.379517, -0.876953], "GYRO": [-2.515625, 1.693359, -0.537109], "Time": 98244}
{"ACCEL": [0.130127, 0.380737, -0.880859], "GYRO": [-3.105469, 1.730469, -0.419922], "Time": 98262}
{"ACCEL": [0.130859, 0.380981, -0.877808], "GYRO": [-3.226562, 1.587891, -0.535156], "Time": 98280}
{"ACCEL": [0.128052, 0.381470, -0.878174], "GYRO": [-2.910156, 1.673828, -0.605469], "Time": 98298}
{"ACCEL": [0.125977, 0.381470, -0.877197], "GYRO": [-3.175781, 1.603516, -0.453125], "Time": 98316}
{"ACCEL": [0.125000, 0.382324, -0.877808], "GYRO": [-2.917969, 1.892578, -0.470703], "Time": 98333}
{"ACCEL": [0.126221, 0.384521, -0.876465], "GYRO": [-2.886719, 1.816406, -0.474609], "Time": 98351}
{"ACCEL": [0.128052, 0.385376, -0.877808], "GYRO": [-3.273438, 1.820312, -0.486328], "Time": 98369}
