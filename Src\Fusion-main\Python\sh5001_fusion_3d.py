import sys
import serial
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from collections import deque
import time
import threading
import queue
import imufusion

class SH5001FusionVisualizer:
    def __init__(self, port='COM6', baudrate=115200):
        """
        SH5001传感器 + Imufusion滤波 + 3D可视化
        
        Args:
            port: 串口端口号 (默认COM6)
            baudrate: 波特率 (默认115200)
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.data_queue = queue.Queue()
        self.running = False
        
        # Imufusion AHRS对象
        self.ahrs = imufusion.Ahrs()
        
        # 数据存储
        self.raw_quaternions = deque(maxlen=500)      # 原始四元数
        self.filtered_quaternions = deque(maxlen=500) # 滤波后四元数
        self.timestamps = deque(maxlen=500)
        
        # 当前四元数
        self.current_raw_quat = np.array([1.0, 0.0, 0.0, 0.0])
        self.current_filtered_quat = np.array([1.0, 0.0, 0.0, 0.0])
        
        # 模拟陀螺仪和加速度计数据（用于Imufusion）
        self.gyro_data = np.array([0.0, 0.0, 0.0])
        self.accel_data = np.array([0.0, 0.0, 1.0])  # 默认重力向下
        
        # 设置图形界面
        self.setup_visualization()
        
    def setup_visualization(self):
        """设置可视化界面"""
        self.fig = plt.figure(figsize=(16, 12))
        
        # 3D可视化 - 原始数据
        self.ax_3d_raw = self.fig.add_subplot(231, projection='3d')
        self.ax_3d_raw.set_title('原始四元数 3D姿态')
        
        # 3D可视化 - 滤波数据
        self.ax_3d_filtered = self.fig.add_subplot(232, projection='3d')
        self.ax_3d_filtered.set_title('Imufusion滤波后 3D姿态')
        
        # 四元数对比
        self.ax_quat_compare = self.fig.add_subplot(233)
        self.ax_quat_compare.set_title('四元数对比 (原始 vs 滤波)')
        
        # 欧拉角对比
        self.ax_euler_compare = self.fig.add_subplot(234)
        self.ax_euler_compare.set_title('欧拉角对比 (原始 vs 滤波)')
        
        # 原始数据流
        self.ax_raw_stream = self.fig.add_subplot(235)
        self.ax_raw_stream.set_title('原始四元数数据流')
        
        # 滤波效果
        self.ax_filter_effect = self.fig.add_subplot(236)
        self.ax_filter_effect.set_title('滤波效果 (四元数模长)')
        
        # 设置3D图形属性
        for ax in [self.ax_3d_raw, self.ax_3d_filtered]:
            ax.set_xlim([-1.5, 1.5])
            ax.set_ylim([-1.5, 1.5])
            ax.set_zlim([-1.5, 1.5])
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
        
        plt.tight_layout()
    
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"✓ 成功连接到 {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            print(f"✗ 串口连接失败: {e}")
            return False
    
    def parse_quaternion_line(self, line):
        """
        解析四元数字符串
        输入格式: "-0.0227,0.8463,-0.5212,-0.1077"
        假设顺序为: w,x,y,z
        """
        try:
            line = line.strip()
            if not line:
                return None
                
            values = line.split(',')
            if len(values) == 4:
                quat = np.array([float(val) for val in values], dtype=np.float32)
                # 归一化四元数
                norm = np.linalg.norm(quat)
                if norm > 0:
                    quat = quat / norm
                return quat
            return None
        except Exception as e:
            print(f"解析错误: {e}, 数据: {line}")
            return None
    
    def quaternion_to_euler(self, quat):
        """四元数转欧拉角 (Roll, Pitch, Yaw) - 度"""
        w, x, y, z = quat
        
        # Roll (x-axis rotation)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sinr_cosp, cosr_cosp)
        
        # Pitch (y-axis rotation)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = np.copysign(np.pi / 2, sinp)
        else:
            pitch = np.arcsin(sinp)
        
        # Yaw (z-axis rotation)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)
        
        return np.degrees([roll, pitch, yaw])
    
    def quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        w, x, y, z = quat
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])
    
    def estimate_gyro_accel_from_quaternion(self, quat_prev, quat_curr, dt):
        """
        从四元数变化估算陀螺仪和加速度计数据
        这是一个简化的方法，用于为Imufusion提供输入
        """
        if dt <= 0:
            return self.gyro_data, self.accel_data
        
        # 计算角速度 (简化方法)
        # 四元数微分: dq/dt = 0.5 * q * ω
        q_diff = quat_curr - quat_prev
        
        # 估算角速度 (这是一个近似方法)
        w, x, y, z = quat_curr
        dw, dx, dy, dz = q_diff / dt
        
        # 从四元数微分估算角速度
        gyro_est = 2 * np.array([
            w * dx - x * dw - y * dz + z * dy,
            w * dy - y * dw - z * dx + x * dz,
            w * dz - z * dw - x * dy + y * dx
        ])
        
        # 从四元数估算重力方向 (加速度计)
        R = self.quaternion_to_rotation_matrix(quat_curr)
        gravity_world = np.array([0, 0, -1])  # 世界坐标系中的重力
        accel_est = R.T @ gravity_world  # 转换到传感器坐标系
        
        return gyro_est, accel_est
    
    def apply_imufusion_filter(self, raw_quat, timestamp):
        """应用Imufusion滤波"""
        if len(self.raw_quaternions) > 0:
            prev_quat = self.raw_quaternions[-1]
            prev_time = self.timestamps[-1]
            dt = timestamp - prev_time
            
            # 估算陀螺仪和加速度计数据
            gyro, accel = self.estimate_gyro_accel_from_quaternion(prev_quat, raw_quat, dt)
            
            # 更新Imufusion AHRS
            self.ahrs.update_no_magnetometer(gyro, accel, dt)
            
            # 获取滤波后的四元数
            filtered_q = self.ahrs.quaternion
            filtered_quat = np.array([filtered_q.w, filtered_q.x, filtered_q.y, filtered_q.z])
            
            return filtered_quat
        else:
            # 第一个数据点，直接返回原始数据
            return raw_quat
    
    def read_serial_data(self):
        """串口数据读取线程"""
        buffer = ""
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data
                    
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一行
                    
                    for line in lines[:-1]:
                        quat = self.parse_quaternion_line(line)
                        if quat is not None:
                            timestamp = time.time()
                            
                            # 应用Imufusion滤波
                            filtered_quat = self.apply_imufusion_filter(quat, timestamp)
                            
                            # 放入队列
                            self.data_queue.put((timestamp, quat, filtered_quat))
                
                time.sleep(0.01)  # 10ms延迟
                
            except Exception as e:
                print(f"串口读取错误: {e}")
                time.sleep(0.1)
    
    def update_visualization(self, frame):
        """更新可视化"""
        # 处理新数据
        while not self.data_queue.empty():
            try:
                timestamp, raw_quat, filtered_quat = self.data_queue.get_nowait()
                self.timestamps.append(timestamp)
                self.raw_quaternions.append(raw_quat)
                self.filtered_quaternions.append(filtered_quat)
                self.current_raw_quat = raw_quat
                self.current_filtered_quat = filtered_quat
            except queue.Empty:
                break
        
        if len(self.raw_quaternions) == 0:
            return
        
        # 清除所有图形
        for ax in [self.ax_3d_raw, self.ax_3d_filtered, self.ax_quat_compare, 
                   self.ax_euler_compare, self.ax_raw_stream, self.ax_filter_effect]:
            ax.clear()
        
        # 重新绘制
        self.draw_3d_comparison()
        self.draw_quaternion_comparison()
        self.draw_euler_comparison()
        self.draw_data_streams()
        
    def draw_3d_comparison(self):
        """绘制3D姿态对比"""
        # 设置3D图形属性
        for ax, title in [(self.ax_3d_raw, '原始四元数 3D姿态'), 
                         (self.ax_3d_filtered, 'Imufusion滤波后 3D姿态')]:
            ax.set_xlim([-1.5, 1.5])
            ax.set_ylim([-1.5, 1.5])
            ax.set_zlim([-1.5, 1.5])
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
            ax.set_title(title)
        
        # 绘制原始数据3D姿态
        self.draw_3d_axes(self.ax_3d_raw, self.current_raw_quat, 'raw')
        
        # 绘制滤波数据3D姿态
        self.draw_3d_axes(self.ax_3d_filtered, self.current_filtered_quat, 'filtered')
    
    def draw_3d_axes(self, ax, quat, data_type):
        """在指定的3D轴上绘制坐标系"""
        R = self.quaternion_to_rotation_matrix(quat)
        
        # 坐标轴向量
        origin = np.array([0, 0, 0])
        x_axis = R @ np.array([1, 0, 0])
        y_axis = R @ np.array([0, 1, 0])
        z_axis = R @ np.array([0, 0, 1])
        
        # 绘制坐标轴
        ax.quiver(origin[0], origin[1], origin[2], 
                 x_axis[0], x_axis[1], x_axis[2], 
                 color='red', arrow_length_ratio=0.1, linewidth=3, label='X轴')
        ax.quiver(origin[0], origin[1], origin[2], 
                 y_axis[0], y_axis[1], y_axis[2], 
                 color='green', arrow_length_ratio=0.1, linewidth=3, label='Y轴')
        ax.quiver(origin[0], origin[1], origin[2], 
                 z_axis[0], z_axis[1], z_axis[2], 
                 color='blue', arrow_length_ratio=0.1, linewidth=3, label='Z轴')
        
        # 绘制传感器立方体
        self.draw_sensor_cube(ax, R)
        
        # 显示四元数值
        w, x, y, z = quat
        ax.text2D(0.05, 0.95, f'Q: [{w:.3f}, {x:.3f}, {y:.3f}, {z:.3f}]', 
                 transform=ax.transAxes, fontsize=8, 
                 bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        ax.legend()
    
    def draw_sensor_cube(self, ax, R):
        """绘制传感器立方体"""
        # 立方体顶点
        vertices = np.array([
            [-0.3, -0.3, -0.3], [0.3, -0.3, -0.3], [0.3, 0.3, -0.3], [-0.3, 0.3, -0.3],
            [-0.3, -0.3, 0.3], [0.3, -0.3, 0.3], [0.3, 0.3, 0.3], [-0.3, 0.3, 0.3]
        ])
        
        # 应用旋转
        rotated_vertices = vertices @ R.T
        
        # 立方体的边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 绘制边
        for edge in edges:
            points = rotated_vertices[edge]
            ax.plot3D(*points.T, 'k-', alpha=0.8, linewidth=2)
    
    def draw_quaternion_comparison(self):
        """绘制四元数对比"""
        if len(self.raw_quaternions) < 2:
            return
        
        raw_quats = np.array(list(self.raw_quaternions))
        filtered_quats = np.array(list(self.filtered_quaternions))
        times = np.array(list(self.timestamps))
        times = times - times[0]  # 相对时间
        
        # 绘制四元数分量
        components = ['w', 'x', 'y', 'z']
        colors = ['red', 'green', 'blue', 'magenta']
        
        for i, (comp, color) in enumerate(zip(components, colors)):
            self.ax_quat_compare.plot(times, raw_quats[:, i], 
                                    color=color, linestyle='-', alpha=0.7, 
                                    label=f'{comp} (原始)', linewidth=1)
            self.ax_quat_compare.plot(times, filtered_quats[:, i], 
                                    color=color, linestyle='--', 
                                    label=f'{comp} (滤波)', linewidth=2)
        
        self.ax_quat_compare.set_title('四元数对比 (原始 vs 滤波)')
        self.ax_quat_compare.set_xlabel('时间 (秒)')
        self.ax_quat_compare.set_ylabel('四元数值')
        self.ax_quat_compare.legend(fontsize=8)
        self.ax_quat_compare.grid(True)
    
    def draw_euler_comparison(self):
        """绘制欧拉角对比"""
        if len(self.raw_quaternions) < 2:
            return
        
        raw_eulers = []
        filtered_eulers = []
        
        for raw_q, filt_q in zip(self.raw_quaternions, self.filtered_quaternions):
            raw_eulers.append(self.quaternion_to_euler(raw_q))
            filtered_eulers.append(self.quaternion_to_euler(filt_q))
        
        raw_eulers = np.array(raw_eulers)
        filtered_eulers = np.array(filtered_eulers)
        times = np.array(list(self.timestamps))
        times = times - times[0]
        
        angles = ['Roll', 'Pitch', 'Yaw']
        colors = ['red', 'green', 'blue']
        
        for i, (angle, color) in enumerate(zip(angles, colors)):
            self.ax_euler_compare.plot(times, raw_eulers[:, i], 
                                     color=color, linestyle='-', alpha=0.7,
                                     label=f'{angle} (原始)', linewidth=1)
            self.ax_euler_compare.plot(times, filtered_eulers[:, i], 
                                     color=color, linestyle='--',
                                     label=f'{angle} (滤波)', linewidth=2)
        
        self.ax_euler_compare.set_title('欧拉角对比 (原始 vs 滤波)')
        self.ax_euler_compare.set_xlabel('时间 (秒)')
        self.ax_euler_compare.set_ylabel('角度 (度)')
        self.ax_euler_compare.legend(fontsize=8)
        self.ax_euler_compare.grid(True)
    
    def draw_data_streams(self):
        """绘制数据流和滤波效果"""
        if len(self.raw_quaternions) < 2:
            return
        
        # 原始数据流 (最近50个点)
        recent_raw = list(self.raw_quaternions)[-50:]
        indices = range(len(recent_raw))
        raw_array = np.array(recent_raw)
        
        for i, comp in enumerate(['w', 'x', 'y', 'z']):
            self.ax_raw_stream.plot(indices, raw_array[:, i], 
                                  '.-', label=comp, markersize=3)
        
        self.ax_raw_stream.set_title('原始四元数数据流 (最近50个点)')
        self.ax_raw_stream.set_xlabel('样本')
        self.ax_raw_stream.set_ylabel('值')
        self.ax_raw_stream.legend()
        self.ax_raw_stream.grid(True)
        
        # 滤波效果 (四元数模长)
        raw_norms = [np.linalg.norm(q) for q in self.raw_quaternions]
        filtered_norms = [np.linalg.norm(q) for q in self.filtered_quaternions]
        times = np.array(list(self.timestamps))
        times = times - times[0]
        
        self.ax_filter_effect.plot(times, raw_norms, 'r-', alpha=0.7, 
                                 label='原始模长', linewidth=1)
        self.ax_filter_effect.plot(times, filtered_norms, 'b--', 
                                 label='滤波模长', linewidth=2)
        self.ax_filter_effect.axhline(y=1.0, color='green', linestyle=':', 
                                    label='理想值 (1.0)')
        
        self.ax_filter_effect.set_title('滤波效果 (四元数模长)')
        self.ax_filter_effect.set_xlabel('时间 (秒)')
        self.ax_filter_effect.set_ylabel('模长')
        self.ax_filter_effect.legend()
        self.ax_filter_effect.grid(True)
    
    def start(self):
        """启动可视化系统"""
        print("🚀 启动SH5001 + Imufusion 3D可视化系统")
        print(f"📡 串口: {self.port}, 波特率: {self.baudrate}")
        print("📊 四元数格式: w,x,y,z")
        print("🔄 Imufusion滤波: 启用")
        print("⏹️  按Ctrl+C或关闭窗口退出")
        
        if not self.connect_serial():
            return
        
        self.running = True
        
        # 启动串口读取线程
        serial_thread = threading.Thread(target=self.read_serial_data)
        serial_thread.daemon = True
        serial_thread.start()
        
        # 启动动画
        ani = animation.FuncAnimation(self.fig, self.update_visualization, 
                                    interval=50, blit=False)
        
        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n⏹️  程序已停止")
        finally:
            self.running = False
            if self.ser:
                self.ser.close()

def main():
    """主函数"""
    try:
        visualizer = SH5001FusionVisualizer(port='COM6', baudrate=115200)
        visualizer.start()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
