import numpy as np
import imufusion
import matplotlib.pyplot as plt
import sys

# 读取传感器数据
data = np.genfromtxt(r"D:\pythonME\Fusion-main\Fusion-main\Python\static_sensor_data.txt", delimiter=",", skip_header=1)

timestamp = data[:, 0]
gyroscope = data[:, 1:4]
accelerometer = data[:, 4:7]

# 创建 AHRS 对象
ahrs = imufusion.Ahrs()

# 存储四元数 (w, x, y, z)
quaternions = np.empty((len(timestamp), 4))

for index in range(len(timestamp)):
    ahrs.update_no_magnetometer(gyroscope[index], accelerometer[index], 1 / 100)  # 100 Hz 采样率
    q = ahrs.quaternion  # 获取四元数
    quat = np.array([q.w, q.x, q.y, q.z])
    quaternions[index] = np.array([q.w, q.x, q.y, q.z])  # 存储四元数数据
    print(q.w)

# 绘制四元数曲线
plt.figure(figsize=(10, 6))
plt.plot(timestamp, quaternions[:, 0], "tab:red", label="W")
plt.plot(timestamp, quaternions[:, 1], "tab:green", label="X")
plt.plot(timestamp, quaternions[:, 2], "tab:blue", label="Y")
plt.plot(timestamp, quaternions[:, 3], "tab:purple", label="Z")

plt.title("Quaternion Components Over Time")
plt.xlabel("Time (ms)")
plt.ylabel("Quaternion Value")
plt.legend()
plt.grid()
plt.show()
