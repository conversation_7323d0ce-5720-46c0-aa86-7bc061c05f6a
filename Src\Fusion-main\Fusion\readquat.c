#include <stdio.h>
#include "FusionAhrs.h"

FusionAhrs ahrs;  // AHRS 结构体

typedef struct {
    float x;
    float y;
    float z;
} SensorVector;

typedef struct {
    SensorVector gyro;
    SensorVector accel;
} IMUData;

void updateQuaternion(IMUData *imuData, float deltaTime) {
    FusionVector gyroscope = {.axis = { imuData->gyro.x, imuData->gyro.y, imuData->gyro.z }};
    FusionVector accelerometer = {.axis = { imuData->accel.x, imuData->accel.y, imuData->accel.z }};
    
    FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, deltaTime);
}

int main() {
    FusionAhrsInitialise(&ahrs);

    IMUData imuData = {
        .gyro = {0.1f, -0.2f, 0.05f},  // 角速度数据
        .accel = {0.0f, 0.0f, 1.0f}    // 加速度数据（假设静止）
    };

    float deltaTime = 0.01f;  // 10ms 采样周期
    updateQuaternion(&imuData, deltaTime);

    FusionQuaternion quaternion = FusionAhrsGetQuaternion(&ahrs);
    printf("Quaternion: [w: %f, x: %f, y: %f, z: %f]\n", 
           quaternion.element.w, quaternion.element.x, quaternion.element.y, quaternion.element.z);

    FusionEulerAngles euler = FusionQuaternionToEulerAngles(quaternion);
    printf("Roll: %f, Pitch: %f, Yaw: %f\n", euler.angle.roll, euler.angle.pitch, euler.angle.yaw);

    return 0;
}
