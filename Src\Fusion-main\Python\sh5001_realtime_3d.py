import sys
import serial
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from collections import deque
import time
import threading
import queue

class SH5001Visualizer:
    def __init__(self, port='COM6', baudrate=115200):
        """
        SH5001传感器实时3D可视化器
        
        Args:
            port: 串口端口号
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.data_queue = queue.Queue()
        self.running = False
        
        # 数据存储
        self.quaternions = deque(maxlen=1000)  # 存储最近1000个四元数
        self.timestamps = deque(maxlen=1000)
        
        # 当前四元数
        self.current_quat = np.array([1.0, 0.0, 0.0, 0.0])  # w, x, y, z
        
        # 3D图形设置
        self.fig = plt.figure(figsize=(15, 10))
        self.ax_3d = self.fig.add_subplot(221, projection='3d')
        self.ax_quat = self.fig.add_subplot(222)
        self.ax_euler = self.fig.add_subplot(223)
        self.ax_raw = self.fig.add_subplot(224)
        
        self.setup_plots()
        
    def setup_plots(self):
        """设置图形界面"""
        # 3D可视化设置
        self.ax_3d.set_xlim([-2, 2])
        self.ax_3d.set_ylim([-2, 2])
        self.ax_3d.set_zlim([-2, 2])
        self.ax_3d.set_xlabel('X')
        self.ax_3d.set_ylabel('Y')
        self.ax_3d.set_zlabel('Z')
        self.ax_3d.set_title('SH5001 3D姿态可视化')
        
        # 四元数图表设置
        self.ax_quat.set_title('四元数实时数据')
        self.ax_quat.set_xlabel('时间')
        self.ax_quat.set_ylabel('四元数值')
        self.ax_quat.grid(True)
        
        # 欧拉角图表设置
        self.ax_euler.set_title('欧拉角实时数据')
        self.ax_euler.set_xlabel('时间')
        self.ax_euler.set_ylabel('角度 (度)')
        self.ax_euler.grid(True)
        
        # 原始四元数数据
        self.ax_raw.set_title('原始四元数数据')
        self.ax_raw.set_xlabel('样本')
        self.ax_raw.set_ylabel('值')
        self.ax_raw.grid(True)
        
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"成功连接到 {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            print(f"串口连接失败: {e}")
            return False
    
    def parse_quaternion(self, data_str):
        """
        解析四元数字符串
        输入格式: "-0.0227,0.8463,-0.5212,-0.1077"
        返回: [w, x, y, z] 格式的numpy数组
        """
        try:
            # 移除可能的空白字符
            data_str = data_str.strip()
            # 分割字符串
            values = data_str.split(',')
            if len(values) == 4:
                # 转换为浮点数
                quat = [float(val) for val in values]
                # 假设输入格式为 [w, x, y, z] 或 [x, y, z, w]
                # 根据实际情况调整顺序
                return np.array(quat, dtype=np.float32)
            else:
                return None
        except Exception as e:
            print(f"四元数解析错误: {e}, 数据: {data_str}")
            return None
    
    def quaternion_to_euler(self, quat):
        """
        四元数转欧拉角 (Roll, Pitch, Yaw)
        输入: [w, x, y, z]
        输出: [roll, pitch, yaw] (度)
        """
        w, x, y, z = quat
        
        # Roll (x-axis rotation)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sinr_cosp, cosr_cosp)
        
        # Pitch (y-axis rotation)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = np.copysign(np.pi / 2, sinp)  # use 90 degrees if out of range
        else:
            pitch = np.arcsin(sinp)
        
        # Yaw (z-axis rotation)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)
        
        return np.degrees([roll, pitch, yaw])
    
    def quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        w, x, y, z = quat
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])
    
    def read_serial_data(self):
        """串口数据读取线程"""
        buffer = ""
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    # 读取串口数据
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data
                    
                    # 按行处理数据
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一行（可能不完整）
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line:
                            quat = self.parse_quaternion(line)
                            if quat is not None:
                                # 归一化四元数
                                quat = quat / np.linalg.norm(quat)
                                timestamp = time.time()
                                self.data_queue.put((timestamp, quat))
                
                time.sleep(0.01)  # 10ms延迟
                
            except Exception as e:
                print(f"串口读取错误: {e}")
                time.sleep(0.1)
    
    def update_plot(self, frame):
        """更新图形显示"""
        # 处理队列中的新数据
        while not self.data_queue.empty():
            try:
                timestamp, quat = self.data_queue.get_nowait()
                self.timestamps.append(timestamp)
                self.quaternions.append(quat)
                self.current_quat = quat
            except queue.Empty:
                break
        
        if len(self.quaternions) == 0:
            return
        
        # 清除所有子图
        self.ax_3d.clear()
        self.ax_quat.clear()
        self.ax_euler.clear()
        self.ax_raw.clear()
        
        # 重新设置3D图
        self.ax_3d.set_xlim([-2, 2])
        self.ax_3d.set_ylim([-2, 2])
        self.ax_3d.set_zlim([-2, 2])
        self.ax_3d.set_xlabel('X')
        self.ax_3d.set_ylabel('Y')
        self.ax_3d.set_zlabel('Z')
        self.ax_3d.set_title('SH5001 3D姿态可视化')
        
        # 绘制3D坐标系
        self.draw_3d_axes()
        
        # 绘制四元数曲线
        if len(self.quaternions) > 1:
            self.draw_quaternion_plot()
            self.draw_euler_plot()
            self.draw_raw_data_plot()
    
    def draw_3d_axes(self):
        """绘制3D坐标轴"""
        # 获取旋转矩阵
        R = self.quaternion_to_rotation_matrix(self.current_quat)
        
        # 原始坐标轴
        origin = np.array([0, 0, 0])
        x_axis = R @ np.array([1, 0, 0])
        y_axis = R @ np.array([0, 1, 0])
        z_axis = R @ np.array([0, 0, 1])
        
        # 绘制坐标轴
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         x_axis[0], x_axis[1], x_axis[2], 
                         color='red', arrow_length_ratio=0.1, linewidth=3, label='X轴')
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         y_axis[0], y_axis[1], y_axis[2], 
                         color='green', arrow_length_ratio=0.1, linewidth=3, label='Y轴')
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         z_axis[0], z_axis[1], z_axis[2], 
                         color='blue', arrow_length_ratio=0.1, linewidth=3, label='Z轴')
        
        # 绘制立方体表示传感器
        self.draw_sensor_cube(R)
        
        self.ax_3d.legend()
    
    def draw_sensor_cube(self, R):
        """绘制表示传感器的立方体"""
        # 立方体顶点（本地坐标）
        vertices = np.array([
            [-0.5, -0.5, -0.5], [0.5, -0.5, -0.5], [0.5, 0.5, -0.5], [-0.5, 0.5, -0.5],
            [-0.5, -0.5, 0.5], [0.5, -0.5, 0.5], [0.5, 0.5, 0.5], [-0.5, 0.5, 0.5]
        ])
        
        # 应用旋转
        rotated_vertices = vertices @ R.T
        
        # 立方体的边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 绘制边
        for edge in edges:
            points = rotated_vertices[edge]
            self.ax_3d.plot3D(*points.T, 'k-', alpha=0.6)
    
    def draw_quaternion_plot(self):
        """绘制四元数曲线"""
        if len(self.quaternions) < 2:
            return
            
        quats = np.array(list(self.quaternions))
        times = np.array(list(self.timestamps))
        times = times - times[0]  # 相对时间
        
        self.ax_quat.plot(times, quats[:, 0], 'r-', label='w', linewidth=2)
        self.ax_quat.plot(times, quats[:, 1], 'g-', label='x', linewidth=2)
        self.ax_quat.plot(times, quats[:, 2], 'b-', label='y', linewidth=2)
        self.ax_quat.plot(times, quats[:, 3], 'm-', label='z', linewidth=2)
        
        self.ax_quat.set_title('四元数实时数据')
        self.ax_quat.set_xlabel('时间 (秒)')
        self.ax_quat.set_ylabel('四元数值')
        self.ax_quat.legend()
        self.ax_quat.grid(True)
    
    def draw_euler_plot(self):
        """绘制欧拉角曲线"""
        if len(self.quaternions) < 2:
            return
            
        eulers = []
        for quat in self.quaternions:
            euler = self.quaternion_to_euler(quat)
            eulers.append(euler)
        
        eulers = np.array(eulers)
        times = np.array(list(self.timestamps))
        times = times - times[0]  # 相对时间
        
        self.ax_euler.plot(times, eulers[:, 0], 'r-', label='Roll', linewidth=2)
        self.ax_euler.plot(times, eulers[:, 1], 'g-', label='Pitch', linewidth=2)
        self.ax_euler.plot(times, eulers[:, 2], 'b-', label='Yaw', linewidth=2)
        
        self.ax_euler.set_title('欧拉角实时数据')
        self.ax_euler.set_xlabel('时间 (秒)')
        self.ax_euler.set_ylabel('角度 (度)')
        self.ax_euler.legend()
        self.ax_euler.grid(True)
    
    def draw_raw_data_plot(self):
        """绘制原始数据"""
        if len(self.quaternions) < 2:
            return
            
        recent_quats = list(self.quaternions)[-50:]  # 最近50个数据点
        indices = range(len(recent_quats))
        
        quats = np.array(recent_quats)
        
        self.ax_raw.plot(indices, quats[:, 0], 'r.-', label='w', markersize=4)
        self.ax_raw.plot(indices, quats[:, 1], 'g.-', label='x', markersize=4)
        self.ax_raw.plot(indices, quats[:, 2], 'b.-', label='y', markersize=4)
        self.ax_raw.plot(indices, quats[:, 3], 'm.-', label='z', markersize=4)
        
        self.ax_raw.set_title('原始四元数数据 (最近50个点)')
        self.ax_raw.set_xlabel('样本')
        self.ax_raw.set_ylabel('值')
        self.ax_raw.legend()
        self.ax_raw.grid(True)
    
    def start(self):
        """启动可视化"""
        if not self.connect_serial():
            return
        
        self.running = True
        
        # 启动串口读取线程
        serial_thread = threading.Thread(target=self.read_serial_data)
        serial_thread.daemon = True
        serial_thread.start()
        
        # 启动动画
        ani = animation.FuncAnimation(self.fig, self.update_plot, interval=50, blit=False)
        
        plt.tight_layout()
        plt.show()
        
        self.running = False
        if self.ser:
            self.ser.close()

def main():
    """主函数"""
    print("SH5001传感器实时3D可视化程序")
    print("串口: COM6, 波特率: 115200")
    print("四元数格式: w,x,y,z")
    print("按Ctrl+C退出程序")
    
    try:
        visualizer = SH5001Visualizer(port='COM6', baudrate=115200)
        visualizer.start()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
