import sys
import serial
import json
import numpy as np
import pygame
from pygame.locals import *
import math
import imufusion

buffer = ''

# Pygame 初始化
pygame.init()
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("3D Visualization with Pygame")
clock = pygame.time.Clock()

# 串口初始化
ser = serial.Serial('COM4', 115200, timeout=1, write_timeout=1, inter_byte_timeout=0.01)
ahrs = imufusion.Ahrs()

# 立方体顶点
vertices = [
    [-1, -1, 1], [1, -1, 1], [1, 1, 1], [-1, 1, 1],
    [-1, -1, -1], [1, -1, -1], [1, 1, -1], [-1, 1, -1]
]

# 立方体边
edges = [
    [0, 1], [1, 2], [2, 3], [3, 0],
    [4, 5], [5, 6], [6, 7], [7, 4],
    [0, 4], [1, 5], [2, 6], [3, 7]
]

# 颜色
colors = [
    (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
    (255, 0, 255), (0, 255, 255), (128, 128, 128), (255, 165, 0)
]

class SensorData:
    def __init__(self, gx, gy, gz, ax, ay, az):
        self.gx = gx
        self.gy = gy
        self.gz = gz
        self.ax = ax
        self.ay = ay
        self.az = az

def update_quat():
    # 更新四元数
    global accel_values # 确保修改全局变量
    # 确保 gyro_values 和 accel_values 是 numpy.array 并且是 float32 类型
    gyro_values = np.array(gyro_values, dtype=np.float32).reshape((3,))
    accel_values = np.array(accel_values, dtype=np.float32).reshape((3,))
    ahrs.update_no_magnetometer(gyro_values, accel_values, 1 / 100)  # 100 Hz 采样率
    q.q0, q.q1, q.q2, q.q3 = ahrs.quaternion.w, ahrs.quaternion.x, ahrs.quaternion.y, ahrs.quaternion.z

def rotate_point(point, q):
    # 使用四元数旋转点
    w, x, y, z = q.q0, q.q1, q.q2, q.q3
    x2, y2, z2 = x**2, y**2, z**2
    rotated = [
        (1 - 2 * y2 - 2 * z2) * point[0] + (2 * x * y - 2 * z * w) * point[1] + (2 * x * z + 2 * y * w) * point[2],
        (2 * x * y + 2 * z * w) * point[0] + (1 - 2 * x2 - 2 * z2) * point[1] + (2 * y * z - 2 * x * w) * point[2],
        (2 * x * z - 2 * y * w) * point[0] + (2 * y * z + 2 * x * w) * point[1] + (1 - 2 * x2 - 2 * y2) * point[2]
    ]
    return rotated


def project_point(point):
    # 3D 投影到 2D 屏幕
    scale = 200 / (point[2] + 5)  # 透视投影
    x = int(point[0] * scale + WIDTH // 2)
    y = int(-point[1] * scale + HEIGHT // 2)
    return x, y


def update_rotation():
    global q, buffer
    if ser.in_waiting > 0:
        line = ser.read(ser.in_waiting).decode('utf-8')
        buffer += line

    try:
        while True:
            start = buffer.find("{")
            end = buffer.find("}", start)
            if start != -1 and end != -1:
                data_str = buffer[start:end + 1]
                buffer = buffer[end + 1:]

                data = json.loads(data_str)
                accel_values = np.array(data['ACCEL'], dtype=np.float32)  # 确保是 NumPy 数组
                gyro_values = np.array(data['GYRO'], dtype=np.float32)  # 确保是 NumPy 数组


                data = SensorData(gyro_values[0], gyro_values[1], gyro_values[2],
                                  accel_values[0], accel_values[1], accel_values[2])
                update_quat()
            else:
                break
    except (json.JSONDecodeError, KeyError) as e:
        print("解析错误:", e, "buffer:", buffer)


def draw_cube():
    rotated_vertices = [rotate_point(v, q) for v in vertices]
    projected_vertices = [project_point(v) for v in rotated_vertices]

    for edge in edges:
        pygame.draw.line(screen, (255, 255, 255), projected_vertices[edge[0]], projected_vertices[edge[1]], 2)



def main():
    global q
    
    q = [1,0,0,0]  # 添加这行，确保 q 在 main() 运行前被初始化
    data = SensorData(0.0, 0.0, 0.0, 0.0, 0.0, 1.0)
    while True:
        for event in pygame.event.get():
            if event.type == QUIT:
                pygame.quit()
                sys.exit()
        # 调用更新函数

        screen.fill((0, 0, 0))
        update_rotation()
        draw_cube()
        pygame.display.flip()
        clock.tick(60)


if __name__ == "__main__":
    main()
