# SH5001传感器3D可视化系统 - 使用指南

## 🎯 项目概述

本项目为您的SH5001 IMU传感器提供了完整的实时3D可视化解决方案，包含以下功能：

✅ **实时3D姿态显示** - 基于四元数的3D坐标系可视化  
✅ **Imufusion数据滤波** - 专业的AHRS算法滤波对比  
✅ **多图表监控** - 四元数、欧拉角、数据状态实时曲线  
✅ **串口通信** - 支持ESP32S3开发板COM6串口接收  
✅ **测试模式** - 无需硬件即可测试程序功能  

## 🚀 快速开始

### 方法1: 使用批处理文件 (推荐)
1. 双击运行 `run_sh5001.bat`
2. 选择运行模式：
   - **选项1**: 高性能版本 (推荐 - 流畅60FPS) ⚡
   - **选项2**: 简化版本 (标准功能)
   - **选项3**: 完整版本 (包含Imufusion滤波)
   - **选项4**: 测试模式 (无需硬件)
   - **选项5**: 坐标轴标定工具

### 方法2: 命令行运行
```bash
# 高性能版本 (推荐 - 解决卡顿问题)
python sh5001_smooth_3d.py

# 简化版本 (标准功能)
python sh5001_simple_3d.py

# 完整版本 (包含滤波)
python sh5001_fusion_3d.py

# 测试模式
python sh5001_smooth_3d.py --test

# 坐标轴标定工具
python sh5001_calibration_config.py
```

## 📡 硬件连接

### 连接步骤
1. **SH5001传感器** → **ESP32S3开发板**
2. **ESP32S3** → **电脑USB端口**
3. 确认串口为 **COM6**，波特率 **115200**

### 数据格式要求
ESP32S3应输出四元数格式：
```
-0.0227,0.8463,-0.5212,-0.1077
```
格式说明：`w,x,y,z` (逗号分隔，每行一个四元数)

## 📊 程序功能说明

### 高性能版本 (sh5001_smooth_3d.py) ⚡ 推荐
- **专为流畅度优化**: 60FPS目标，解决卡顿问题
- **全屏3D显示**: 大尺寸3D姿态可视化
- **实时FPS显示**: 监控程序性能
- **坐标轴标定**: 内置快捷键标定功能
- **黑色主题**: 减少渲染负担，提高性能
- **高效数据处理**: 优化的队列和渲染机制

### 简化版本 (sh5001_simple_3d.py)
- **左上角**: 3D姿态实时显示
- **右上角**: 坐标轴标定控制面板
- **左下角**: 实时数据监控
- **标定功能**: 完整的坐标轴标定系统

### 完整版本 (sh5001_fusion_3d.py)
- **原始vs滤波对比**: 6个子图显示详细对比
- **Imufusion滤波**: AHRS算法数据滤波
- **滤波效果分析**: 数据质量和稳定性分析

### 坐标轴标定工具 (sh5001_calibration_config.py)
- **交互式标定**: 向导式标定配置
- **预设配置**: 常用标定方案
- **配置保存/加载**: 持久化标定参数

## 🎯 坐标轴标定功能

### 为什么需要标定？
- **坐标系不匹配**: 传感器坐标系与实际应用坐标系可能不同
- **旋转方向相反**: 四元数可能需要共轭处理
- **轴向不一致**: X/Y/Z轴方向可能需要调整
- **安装方向**: 传感器可能旋转安装

### 标定方法

#### 方法1: 实时键盘标定 (推荐)
在高性能版本中使用键盘快捷键：
- **T**: 切换共轭四元数 (改变旋转方向)
- **Q/W/E**: 反转X/Y/Z轴方向
- **SPACE**: 重置所有标定参数

#### 方法2: 标定工具
运行 `python sh5001_calibration_config.py`：
1. 选择预设配置或自定义
2. 按向导完成标定
3. 保存配置供后续使用

#### 方法3: 配置文件
编辑 `sh5001_calibration.json`：
```json
{
  "axis_mapping": {"x": 0, "y": 1, "z": 2},
  "axis_invert": {"x": false, "y": false, "z": true},
  "quat_order": [0, 1, 2, 3],
  "quat_conjugate": true,
  "description": "自定义配置"
}
```

### 常用标定场景

#### 场景1: 旋转方向相反
**现象**: 向右转但显示向左转
**解决**: 按 `T` 键启用共轭四元数

#### 场景2: 某个轴方向相反
**现象**: 向前倾但显示向后倾
**解决**: 按对应轴的反转键 (Q/W/E)

#### 场景3: 传感器旋转90度安装
**现象**: X轴运动显示为Y轴运动
**解决**: 使用标定工具重新映射轴

## 🔧 故障排除

### 性能问题

#### 1. 画面卡顿
**现象**: 3D显示不流畅，帧率低

**解决方案**:
- **使用高性能版本**: `python sh5001_smooth_3d.py`
- 关闭其他占用GPU的程序
- 降低数据传输频率
- 检查串口数据是否过于频繁

#### 2. 内存占用过高
**现象**: 程序运行一段时间后内存持续增长

**解决方案**:
- 重启程序
- 使用高性能版本 (有内存管理优化)
- 减少历史数据保存量

### 连接问题

#### 3. 串口连接失败
**现象**: `❌ 串口连接失败: [Errno 2] could not open port 'COM6'`

**解决方案**:
- 检查ESP32S3是否正确连接
- 确认设备管理器中的串口号
- 关闭其他可能占用串口的程序
- 重新插拔USB线

#### 4. 没有数据显示
**现象**: 程序运行但图表无数据

**解决方案**:
- 检查ESP32S3是否正在发送数据
- 确认数据格式是否正确 (四个逗号分隔的浮点数)
- 使用串口调试工具检查原始数据

### 显示问题

#### 5. 中文字体警告
**现象**: `Glyph missing from font(s) DejaVu Sans Mono`

**解决方案**:
- 这是字体警告，不影响程序功能
- 可以忽略，或安装中文字体包

#### 6. 程序崩溃
**现象**: 程序意外退出

**解决方案**:
- 检查Python版本 (需要3.7+)
- 重新安装依赖: `python setup_and_run.py`
- 使用测试模式验证程序: `python sh5001_smooth_3d.py --test`

## 🛠️ 高级配置

### 修改串口设置
编辑程序文件，修改以下行：
```python
visualizer = SH5001Simple3D(port='COM7', baudrate=9600)
```

### 调整数据历史长度
```python
self.max_history = 500  # 保存500个数据点
```

### 修改更新频率
```python
interval=50  # 50ms更新一次 (20Hz)
```

## 📈 数据解读

### 四元数 (w, x, y, z)
- **w**: 标量部分，表示旋转角度
- **x, y, z**: 向量部分，表示旋转轴
- **模长**: 应该接近1.0 (归一化四元数)

### 欧拉角
- **Roll**: 绕X轴旋转 (翻滚角)
- **Pitch**: 绕Y轴旋转 (俯仰角)  
- **Yaw**: 绕Z轴旋转 (偏航角)

### 3D坐标系
- **红色箭头**: X轴
- **绿色箭头**: Y轴
- **蓝色箭头**: Z轴
- **黑色立方体**: 传感器本体
- **红点**: 传感器正面标记

## 🧪 测试和验证

### 使用测试模式
```bash
python sh5001_simple_3d.py --test
```
测试模式会生成模拟的旋转数据，用于验证程序功能。

### 验证数据质量
1. **四元数模长**: 应该稳定在1.0附近
2. **数据连续性**: 曲线应该平滑，无突跳
3. **更新频率**: 检查数据更新是否及时

## 📞 技术支持

### 获取帮助
1. 查看 `README_SH5001.md` 详细文档
2. 检查代码注释和错误信息
3. 使用测试模式排除硬件问题

### 常用调试命令
```bash
# 检查Python版本
python --version

# 检查依赖安装
python -c "import numpy, matplotlib, serial, imufusion; print('所有依赖已安装')"

# 列出可用串口
python -c "import serial.tools.list_ports; [print(p.device, p.description) for p in serial.tools.list_ports.comports()]"
```

## 🎉 使用技巧

1. **首次使用**: 建议先运行测试模式熟悉界面
2. **数据调试**: 使用串口调试工具检查原始数据格式
3. **性能优化**: 如果电脑性能较低，可以增加更新间隔
4. **数据记录**: 程序支持实时显示，如需记录数据可修改代码添加保存功能

---

**祝您使用愉快！如有问题请参考故障排除部分或查看详细文档。** 🚀
