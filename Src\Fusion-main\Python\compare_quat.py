import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R

# 读取数据
data = np.genfromtxt(r"D:\pythonME\Fusion-main\Fusion-main\Python\static_sensor_data.txt", delimiter=",", skip_header=1)

timestamp = data[:, 0]
gyroscope = data[:, 1:4]  # 单位：度每秒
accelerometer = data[:, 4:7]

# 初始化四元数数组
quaternions = np.zeros((len(timestamp), 4))
quaternions[0] = [1, 0, 0, 0]  # 初始四元数 (单位四元数)

# 计算时间间隔
dt = np.diff(timestamp, prepend=timestamp[0])  # 计算相邻时间戳的间隔

# 逐步积分陀螺仪数据
for i in range(1, len(timestamp)):
    omega = gyroscope[i] * (np.pi / 180)  # 转换为弧度/秒
    norm_omega = np.linalg.norm(omega)
    
    if norm_omega > 0:
        delta_theta = norm_omega * dt[i]
        delta_q = R.from_rotvec((omega / norm_omega) * delta_theta).as_quat()  # 旋转向量转四元数

        # 更新四元数
        prev_q = R.from_quat(quaternions[i - 1])
        new_q = prev_q * R.from_quat(delta_q)
        quaternions[i] = new_q.as_quat()
    else:
        quaternions[i] = quaternions[i - 1]  # 如果角速度为零，则四元数不变

# 绘制四元数曲线
plt.figure(figsize=(10, 6))
plt.plot(timestamp, quaternions[:, 0], "tab:red", label="W")
plt.plot(timestamp, quaternions[:, 1], "tab:green", label="X")
plt.plot(timestamp, quaternions[:, 2], "tab:blue", label="Y")
plt.plot(timestamp, quaternions[:, 3], "tab:purple", label="Z")

plt.title("Raw Quaternion Components Over Time (Without Filtering)")
plt.xlabel("Time (seconds)")
plt.ylabel("Quaternion Value")
plt.legend()
plt.grid()
plt.show()
