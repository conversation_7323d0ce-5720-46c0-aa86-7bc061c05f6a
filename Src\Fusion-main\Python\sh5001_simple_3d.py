import sys
import serial
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import time
import threading
import queue

class SH5001Simple3D:
    def __init__(self, port='COM6', baudrate=115200):
        """
        SH5001传感器简化3D可视化
        直接使用ESP32S3输出的四元数进行可视化
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        
        # 数据队列
        self.data_queue = queue.Queue()
        
        # 当前四元数 [w, x, y, z]
        self.current_quat = np.array([1.0, 0.0, 0.0, 0.0])
        
        # 数据历史
        self.quat_history = []
        self.time_history = []
        self.max_history = 200  # 保存最近200个数据点
        
        # 设置图形
        self.setup_visualization()
        
    def setup_visualization(self):
        """设置可视化界面"""
        self.fig = plt.figure(figsize=(14, 10))
        
        # 3D姿态显示
        self.ax_3d = self.fig.add_subplot(221, projection='3d')
        self.ax_3d.set_title('SH5001 实时3D姿态', fontsize=14, fontweight='bold')
        
        # 四元数曲线
        self.ax_quat = self.fig.add_subplot(222)
        self.ax_quat.set_title('四元数实时曲线', fontsize=12)
        
        # 欧拉角曲线
        self.ax_euler = self.fig.add_subplot(223)
        self.ax_euler.set_title('欧拉角实时曲线', fontsize=12)
        
        # 数据状态
        self.ax_status = self.fig.add_subplot(224)
        self.ax_status.set_title('数据状态监控', fontsize=12)
        
        # 设置3D图形
        self.ax_3d.set_xlim([-1.5, 1.5])
        self.ax_3d.set_ylim([-1.5, 1.5])
        self.ax_3d.set_zlim([-1.5, 1.5])
        self.ax_3d.set_xlabel('X轴')
        self.ax_3d.set_ylabel('Y轴')
        self.ax_3d.set_zlabel('Z轴')
        
        plt.tight_layout()
        
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"✓ 成功连接串口 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def parse_quaternion(self, line):
        """
        解析四元数字符串
        输入格式: "-0.0227,0.8463,-0.5212,-0.1077"
        假设顺序为: w,x,y,z
        """
        try:
            line = line.strip()
            if not line:
                return None
            
            # 分割并转换为浮点数
            values = [float(x.strip()) for x in line.split(',')]
            
            if len(values) == 4:
                quat = np.array(values, dtype=np.float32)
                
                # 归一化四元数
                norm = np.linalg.norm(quat)
                if norm > 0.001:  # 避免除零
                    quat = quat / norm
                    return quat
                    
            return None
            
        except Exception as e:
            print(f"解析错误: {e}, 数据: {line}")
            return None
    
    def quaternion_to_euler(self, quat):
        """四元数转欧拉角 (度)"""
        w, x, y, z = quat
        
        # Roll (绕X轴旋转)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sinr_cosp, cosr_cosp)
        
        # Pitch (绕Y轴旋转)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = np.copysign(np.pi / 2, sinp)
        else:
            pitch = np.arcsin(sinp)
        
        # Yaw (绕Z轴旋转)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)
        
        return np.degrees([roll, pitch, yaw])
    
    def quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        w, x, y, z = quat
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])
    
    def read_serial_thread(self):
        """串口读取线程"""
        buffer = ""
        
        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    # 读取数据
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data
                    
                    # 按行处理
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一行
                    
                    for line in lines[:-1]:
                        quat = self.parse_quaternion(line)
                        if quat is not None:
                            timestamp = time.time()
                            self.data_queue.put((timestamp, quat))
                
                time.sleep(0.01)  # 10ms延迟
                
            except Exception as e:
                print(f"串口读取错误: {e}")
                time.sleep(0.1)
    
    def update_visualization(self, frame):
        """更新可视化"""
        # 处理新数据
        new_data_count = 0
        while not self.data_queue.empty():
            try:
                timestamp, quat = self.data_queue.get_nowait()
                
                # 更新当前四元数
                self.current_quat = quat
                
                # 添加到历史记录
                self.time_history.append(timestamp)
                self.quat_history.append(quat)
                new_data_count += 1
                
                # 限制历史记录长度
                if len(self.quat_history) > self.max_history:
                    self.quat_history.pop(0)
                    self.time_history.pop(0)
                    
            except queue.Empty:
                break
        
        # 如果没有数据，返回
        if len(self.quat_history) == 0:
            return
        
        # 清除所有图形
        self.ax_3d.clear()
        self.ax_quat.clear()
        self.ax_euler.clear()
        self.ax_status.clear()
        
        # 重新绘制
        self.draw_3d_pose()
        self.draw_quaternion_curves()
        self.draw_euler_curves()
        self.draw_status_info(new_data_count)
    
    def draw_3d_pose(self):
        """绘制3D姿态"""
        # 设置3D图形属性
        self.ax_3d.set_xlim([-1.5, 1.5])
        self.ax_3d.set_ylim([-1.5, 1.5])
        self.ax_3d.set_zlim([-1.5, 1.5])
        self.ax_3d.set_xlabel('X轴')
        self.ax_3d.set_ylabel('Y轴')
        self.ax_3d.set_zlabel('Z轴')
        self.ax_3d.set_title('SH5001 实时3D姿态')
        
        # 获取旋转矩阵
        R = self.quaternion_to_rotation_matrix(self.current_quat)
        
        # 绘制坐标轴
        origin = np.array([0, 0, 0])
        
        # X轴 (红色)
        x_axis = R @ np.array([1, 0, 0])
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         x_axis[0], x_axis[1], x_axis[2], 
                         color='red', arrow_length_ratio=0.1, linewidth=4, label='X轴')
        
        # Y轴 (绿色)
        y_axis = R @ np.array([0, 1, 0])
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         y_axis[0], y_axis[1], y_axis[2], 
                         color='green', arrow_length_ratio=0.1, linewidth=4, label='Y轴')
        
        # Z轴 (蓝色)
        z_axis = R @ np.array([0, 0, 1])
        self.ax_3d.quiver(origin[0], origin[1], origin[2], 
                         z_axis[0], z_axis[1], z_axis[2], 
                         color='blue', arrow_length_ratio=0.1, linewidth=4, label='Z轴')
        
        # 绘制传感器盒子
        self.draw_sensor_box(R)
        
        # 显示当前四元数值
        w, x, y, z = self.current_quat
        self.ax_3d.text2D(0.02, 0.98, f'四元数: [{w:.3f}, {x:.3f}, {y:.3f}, {z:.3f}]', 
                         transform=self.ax_3d.transAxes, fontsize=10,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
        # 显示欧拉角
        euler = self.quaternion_to_euler(self.current_quat)
        self.ax_3d.text2D(0.02, 0.90, f'欧拉角: Roll={euler[0]:.1f}°, Pitch={euler[1]:.1f}°, Yaw={euler[2]:.1f}°', 
                         transform=self.ax_3d.transAxes, fontsize=10,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8))
        
        self.ax_3d.legend()
    
    def draw_sensor_box(self, R):
        """绘制传感器盒子"""
        # 盒子顶点 (本地坐标)
        size = 0.4
        vertices = np.array([
            [-size, -size, -size], [size, -size, -size], [size, size, -size], [-size, size, -size],
            [-size, -size, size], [size, -size, size], [size, size, size], [-size, size, size]
        ])
        
        # 应用旋转
        rotated_vertices = vertices @ R.T
        
        # 盒子的边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 绘制边
        for edge in edges:
            points = rotated_vertices[edge]
            self.ax_3d.plot3D(*points.T, 'k-', alpha=0.7, linewidth=2)
        
        # 标记正面 (添加一个小的标记)
        front_center = R @ np.array([size*0.8, 0, 0])
        self.ax_3d.scatter(*front_center, color='red', s=50, alpha=0.8)
    
    def draw_quaternion_curves(self):
        """绘制四元数曲线"""
        if len(self.quat_history) < 2:
            return
        
        quats = np.array(self.quat_history)
        times = np.array(self.time_history)
        times = times - times[0]  # 相对时间
        
        # 绘制四个分量
        self.ax_quat.plot(times, quats[:, 0], 'r-', label='w', linewidth=2)
        self.ax_quat.plot(times, quats[:, 1], 'g-', label='x', linewidth=2)
        self.ax_quat.plot(times, quats[:, 2], 'b-', label='y', linewidth=2)
        self.ax_quat.plot(times, quats[:, 3], 'm-', label='z', linewidth=2)
        
        self.ax_quat.set_title('四元数实时曲线')
        self.ax_quat.set_xlabel('时间 (秒)')
        self.ax_quat.set_ylabel('四元数值')
        self.ax_quat.legend()
        self.ax_quat.grid(True, alpha=0.3)
        self.ax_quat.set_ylim([-1.1, 1.1])
    
    def draw_euler_curves(self):
        """绘制欧拉角曲线"""
        if len(self.quat_history) < 2:
            return
        
        # 计算欧拉角
        eulers = []
        for quat in self.quat_history:
            euler = self.quaternion_to_euler(quat)
            eulers.append(euler)
        
        eulers = np.array(eulers)
        times = np.array(self.time_history)
        times = times - times[0]
        
        # 绘制三个角度
        self.ax_euler.plot(times, eulers[:, 0], 'r-', label='Roll', linewidth=2)
        self.ax_euler.plot(times, eulers[:, 1], 'g-', label='Pitch', linewidth=2)
        self.ax_euler.plot(times, eulers[:, 2], 'b-', label='Yaw', linewidth=2)
        
        self.ax_euler.set_title('欧拉角实时曲线')
        self.ax_euler.set_xlabel('时间 (秒)')
        self.ax_euler.set_ylabel('角度 (度)')
        self.ax_euler.legend()
        self.ax_euler.grid(True, alpha=0.3)
    
    def draw_status_info(self, new_data_count):
        """绘制状态信息"""
        self.ax_status.axis('off')
        
        # 状态信息
        status_text = f"""
📡 串口状态: {'连接' if self.ser and self.ser.is_open else '断开'}
📊 数据点数: {len(self.quat_history)}
🔄 更新频率: {new_data_count} 点/帧
⏱️ 运行时间: {time.time() - self.start_time:.1f} 秒

📈 当前数据:
   四元数: [{self.current_quat[0]:.3f}, {self.current_quat[1]:.3f}, 
           {self.current_quat[2]:.3f}, {self.current_quat[3]:.3f}]
   模长: {np.linalg.norm(self.current_quat):.4f}
   
🎯 欧拉角:
   Roll:  {self.quaternion_to_euler(self.current_quat)[0]:.1f}°
   Pitch: {self.quaternion_to_euler(self.current_quat)[1]:.1f}°
   Yaw:   {self.quaternion_to_euler(self.current_quat)[2]:.1f}°
        """
        
        self.ax_status.text(0.05, 0.95, status_text, transform=self.ax_status.transAxes,
                           fontsize=10, verticalalignment='top', fontfamily='monospace',
                           bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        self.ax_status.set_title('数据状态监控')
    
    def start(self):
        """启动可视化系统"""
        print("🚀 启动SH5001简化3D可视化系统")
        print(f"📡 串口: {self.port}, 波特率: {self.baudrate}")
        print("📊 四元数格式: w,x,y,z (逗号分隔)")
        print("⏹️  按Ctrl+C或关闭窗口退出")
        
        if not self.connect_serial():
            print("❌ 无法连接串口，请检查设备连接")
            return
        
        self.running = True
        self.start_time = time.time()
        
        # 启动串口读取线程
        serial_thread = threading.Thread(target=self.read_serial_thread)
        serial_thread.daemon = True
        serial_thread.start()
        
        # 启动动画
        ani = animation.FuncAnimation(self.fig, self.update_visualization, 
                                    interval=50, blit=False)
        
        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n⏹️  程序已停止")
        finally:
            self.running = False
            if self.ser:
                self.ser.close()

def test_mode():
    """测试模式 - 使用模拟数据"""
    print("🧪 测试模式 - 使用模拟数据")

    import math

    class TestVisualizer(SH5001Simple3D):
        def __init__(self):
            super().__init__()
            self.test_time = 0

        def connect_serial(self):
            print("✓ 测试模式 - 跳过串口连接")
            return True

        def read_serial_thread(self):
            """模拟串口数据"""
            while self.running:
                # 生成模拟四元数数据 (绕Z轴旋转)
                angle = self.test_time * 0.1  # 慢速旋转
                w = math.cos(angle / 2)
                x = 0
                y = 0
                z = math.sin(angle / 2)

                # 添加一些噪声
                noise = 0.01
                w += (np.random.random() - 0.5) * noise
                x += (np.random.random() - 0.5) * noise
                y += (np.random.random() - 0.5) * noise
                z += (np.random.random() - 0.5) * noise

                quat = np.array([w, x, y, z])
                quat = quat / np.linalg.norm(quat)  # 归一化

                timestamp = time.time()
                self.data_queue.put((timestamp, quat))

                self.test_time += 1
                time.sleep(0.05)  # 20Hz

    visualizer = TestVisualizer()
    visualizer.start()

def main():
    """主函数"""
    print("🎯 SH5001传感器简化3D可视化程序")
    print("="*50)

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        test_mode()
        return

    try:
        visualizer = SH5001Simple3D(port='COM6', baudrate=115200)
        visualizer.start()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        print("💡 提示: 如果没有连接传感器，可以使用测试模式:")
        print("   python sh5001_simple_3d.py --test")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
