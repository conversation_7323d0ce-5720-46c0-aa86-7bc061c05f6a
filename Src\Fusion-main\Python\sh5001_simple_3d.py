import sys
import serial
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import time
import threading
import queue

# 尝试导入标定配置模块
try:
    from sh5001_calibration_config import CalibrationConfig
    CALIBRATION_AVAILABLE = True
except ImportError:
    CALIBRATION_AVAILABLE = False
    print("⚠️ 标定配置模块未找到，使用内置标定功能")

class SH5001Simple3D:
    def __init__(self, port='COM6', baudrate=115200):
        """
        SH5001传感器简化3D可视化
        直接使用ESP32S3输出的四元数进行可视化
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False

        # 数据队列
        self.data_queue = queue.Queue()

        # 当前四元数 [w, x, y, z]
        self.current_quat = np.array([1.0, 0.0, 0.0, 0.0])

        # 数据历史 - 减少历史数据量提高性能
        self.quat_history = []
        self.time_history = []
        self.max_history = 100  # 减少到100个数据点提高性能

        # 坐标轴标定参数
        self.axis_mapping = {'x': 0, 'y': 1, 'z': 2}  # 默认映射
        self.axis_invert = {'x': False, 'y': False, 'z': False}  # 轴反向
        self.quat_order = [0, 1, 2, 3]  # 四元数顺序 [w, x, y, z]
        self.quat_conjugate = False  # 是否使用共轭四元数

        # 性能优化参数
        self.update_interval = 33  # 30FPS (33ms)
        self.data_skip_count = 0  # 跳帧计数
        self.data_skip_rate = 1   # 每N帧处理一次数据

        # 标定配置管理
        if CALIBRATION_AVAILABLE:
            self.calibration_config = CalibrationConfig()
            self.load_calibration_config()

        # 设置图形
        self.setup_visualization()

    def load_calibration_config(self):
        """加载标定配置"""
        if CALIBRATION_AVAILABLE:
            config = self.calibration_config.load_config()
            self.axis_mapping = config.get('axis_mapping', {'x': 0, 'y': 1, 'z': 2})
            self.axis_invert = config.get('axis_invert', {'x': False, 'y': False, 'z': False})
            self.quat_order = config.get('quat_order', [0, 1, 2, 3])
            self.quat_conjugate = config.get('quat_conjugate', False)
            print(f"✓ 已加载标定配置: {config.get('description', '默认')}")

    def save_calibration_config(self):
        """保存当前标定配置"""
        if CALIBRATION_AVAILABLE:
            config = {
                'axis_mapping': self.axis_mapping,
                'axis_invert': self.axis_invert,
                'quat_order': self.quat_order,
                'quat_conjugate': self.quat_conjugate,
            }
            self.calibration_config.save_config(config, "运行时保存的配置")
        
    def setup_visualization(self):
        """设置可视化界面"""
        # 使用更高效的后端
        plt.rcParams['figure.max_open_warning'] = 0
        plt.rcParams['animation.html'] = 'none'

        self.fig = plt.figure(figsize=(16, 10))

        # 3D姿态显示 - 占据更大空间
        self.ax_3d = self.fig.add_subplot(121, projection='3d')
        self.ax_3d.set_title('SH5001 实时3D姿态 (可标定)', fontsize=14, fontweight='bold')

        # 右侧控制面板
        self.ax_control = self.fig.add_subplot(222)
        self.ax_control.set_title('坐标轴标定控制', fontsize=12)

        # 数据监控
        self.ax_data = self.fig.add_subplot(224)
        self.ax_data.set_title('实时数据监控', fontsize=12)

        # 设置3D图形
        self.ax_3d.set_xlim([-1.5, 1.5])
        self.ax_3d.set_ylim([-1.5, 1.5])
        self.ax_3d.set_zlim([-1.5, 1.5])
        self.ax_3d.set_xlabel('X轴')
        self.ax_3d.set_ylabel('Y轴')
        self.ax_3d.set_zlabel('Z轴')

        # 添加标定说明文本
        self.setup_calibration_controls()

        plt.tight_layout()

    def setup_calibration_controls(self):
        """设置标定控制说明"""
        self.ax_control.axis('off')
        control_text = """
坐标轴标定控制 (键盘快捷键):

轴映射:
1/2/3 - 切换X轴映射 (X/Y/Z)
4/5/6 - 切换Y轴映射 (X/Y/Z)
7/8/9 - 切换Z轴映射 (X/Y/Z)

轴反向:
Q/W/E - 反转X/Y/Z轴方向

四元数:
R - 切换四元数顺序
T - 使用共轭四元数

配置管理:
S - 保存当前标定配置
L - 重新加载配置

其他:
SPACE - 重置为默认设置
ESC - 退出程序
        """
        self.ax_control.text(0.05, 0.95, control_text, transform=self.ax_control.transAxes,
                           fontsize=9, verticalalignment='top', fontfamily='monospace',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
    def connect_serial(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"✓ 成功连接串口 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 串口连接失败: {e}")
            return False
    
    def parse_quaternion(self, line):
        """
        解析四元数字符串并应用标定参数
        输入格式: "-0.0227,0.8463,-0.5212,-0.1077"
        """
        try:
            line = line.strip()
            if not line:
                return None

            # 分割并转换为浮点数
            values = [float(x.strip()) for x in line.split(',')]

            if len(values) == 4:
                # 按照设定的顺序重排四元数
                quat = np.array([values[i] for i in self.quat_order], dtype=np.float32)

                # 如果使用共轭四元数 (旋转方向相反)
                if self.quat_conjugate:
                    quat[1:] = -quat[1:]  # 对x,y,z分量取负

                # 归一化四元数
                norm = np.linalg.norm(quat)
                if norm > 0.001:  # 避免除零
                    quat = quat / norm
                    return quat

            return None

        except Exception as e:
            print(f"解析错误: {e}, 数据: {line}")
            return None

    def apply_axis_calibration(self, rotation_matrix):
        """应用坐标轴标定"""
        # 创建轴映射矩阵
        mapping_matrix = np.zeros((3, 3))

        # 设置轴映射
        for axis, target_idx in self.axis_mapping.items():
            source_idx = {'x': 0, 'y': 1, 'z': 2}[axis]
            sign = -1 if self.axis_invert[axis] else 1
            mapping_matrix[source_idx, target_idx] = sign

        # 应用映射
        return mapping_matrix @ rotation_matrix
    
    def quaternion_to_euler(self, quat):
        """四元数转欧拉角 (度)"""
        w, x, y, z = quat
        
        # Roll (绕X轴旋转)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sinr_cosp, cosr_cosp)
        
        # Pitch (绕Y轴旋转)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = np.copysign(np.pi / 2, sinp)
        else:
            pitch = np.arcsin(sinp)
        
        # Yaw (绕Z轴旋转)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)
        
        return np.degrees([roll, pitch, yaw])
    
    def quaternion_to_rotation_matrix(self, quat):
        """四元数转旋转矩阵"""
        w, x, y, z = quat
        return np.array([
            [1-2*(y*y+z*z), 2*(x*y-w*z), 2*(x*z+w*y)],
            [2*(x*y+w*z), 1-2*(x*x+z*z), 2*(y*z-w*x)],
            [2*(x*z-w*y), 2*(y*z+w*x), 1-2*(x*x+y*y)]
        ])
    
    def read_serial_thread(self):
        """串口读取线程 - 优化性能"""
        buffer = ""

        while self.running:
            try:
                if self.ser and self.ser.in_waiting > 0:
                    # 读取数据
                    data = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                    buffer += data

                    # 按行处理
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一行

                    for line in lines[:-1]:
                        # 跳帧处理以提高性能
                        self.data_skip_count += 1
                        if self.data_skip_count % self.data_skip_rate != 0:
                            continue

                        quat = self.parse_quaternion(line)
                        if quat is not None:
                            timestamp = time.time()
                            # 限制队列大小防止内存积累
                            if self.data_queue.qsize() < 10:
                                self.data_queue.put((timestamp, quat))

                time.sleep(0.005)  # 5ms延迟，提高响应速度

            except Exception as e:
                print(f"串口读取错误: {e}")
                time.sleep(0.1)
    
    def update_visualization(self, frame):
        """更新可视化 - 性能优化版本"""
        # 处理新数据
        new_data_count = 0
        while not self.data_queue.empty():
            try:
                timestamp, quat = self.data_queue.get_nowait()

                # 更新当前四元数
                self.current_quat = quat

                # 添加到历史记录
                self.time_history.append(timestamp)
                self.quat_history.append(quat)
                new_data_count += 1

                # 限制历史记录长度
                if len(self.quat_history) > self.max_history:
                    self.quat_history.pop(0)
                    self.time_history.pop(0)

            except queue.Empty:
                break

        # 如果没有数据，返回
        if len(self.quat_history) == 0:
            return

        # 只清除需要更新的图形，减少重绘
        self.ax_3d.clear()
        self.ax_data.clear()

        # 重新绘制
        self.draw_3d_pose()
        self.draw_data_monitor(new_data_count)

        # 处理键盘事件
        self.handle_keyboard_events()
    
    def draw_3d_pose(self):
        """绘制3D姿态 - 应用坐标轴标定"""
        # 设置3D图形属性
        self.ax_3d.set_xlim([-1.5, 1.5])
        self.ax_3d.set_ylim([-1.5, 1.5])
        self.ax_3d.set_zlim([-1.5, 1.5])
        self.ax_3d.set_xlabel('X轴 (标定后)')
        self.ax_3d.set_ylabel('Y轴 (标定后)')
        self.ax_3d.set_zlabel('Z轴 (标定后)')
        self.ax_3d.set_title('SH5001 实时3D姿态 (可标定)')

        # 获取旋转矩阵并应用标定
        R = self.quaternion_to_rotation_matrix(self.current_quat)
        R_calibrated = self.apply_axis_calibration(R)

        # 绘制坐标轴
        origin = np.array([0, 0, 0])

        # X轴 (红色)
        x_axis = R_calibrated @ np.array([1, 0, 0])
        self.ax_3d.quiver(origin[0], origin[1], origin[2],
                         x_axis[0], x_axis[1], x_axis[2],
                         color='red', arrow_length_ratio=0.1, linewidth=4, label='X轴')

        # Y轴 (绿色)
        y_axis = R_calibrated @ np.array([0, 1, 0])
        self.ax_3d.quiver(origin[0], origin[1], origin[2],
                         y_axis[0], y_axis[1], y_axis[2],
                         color='green', arrow_length_ratio=0.1, linewidth=4, label='Y轴')

        # Z轴 (蓝色)
        z_axis = R_calibrated @ np.array([0, 0, 1])
        self.ax_3d.quiver(origin[0], origin[1], origin[2],
                         z_axis[0], z_axis[1], z_axis[2],
                         color='blue', arrow_length_ratio=0.1, linewidth=4, label='Z轴')

        # 绘制传感器盒子
        self.draw_sensor_box(R_calibrated)

        # 显示当前四元数值
        w, x, y, z = self.current_quat
        self.ax_3d.text2D(0.02, 0.98, f'四元数: [{w:.3f}, {x:.3f}, {y:.3f}, {z:.3f}]',
                         transform=self.ax_3d.transAxes, fontsize=9,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))

        # 显示标定状态
        calib_info = f'标定: 共轭={self.quat_conjugate}, X反向={self.axis_invert["x"]}, Y反向={self.axis_invert["y"]}, Z反向={self.axis_invert["z"]}'
        self.ax_3d.text2D(0.02, 0.90, calib_info,
                         transform=self.ax_3d.transAxes, fontsize=8,
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8))

        self.ax_3d.legend()
    
    def draw_sensor_box(self, R):
        """绘制传感器盒子"""
        # 盒子顶点 (本地坐标)
        size = 0.4
        vertices = np.array([
            [-size, -size, -size], [size, -size, -size], [size, size, -size], [-size, size, -size],
            [-size, -size, size], [size, -size, size], [size, size, size], [-size, size, size]
        ])
        
        # 应用旋转
        rotated_vertices = vertices @ R.T
        
        # 盒子的边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        # 绘制边
        for edge in edges:
            points = rotated_vertices[edge]
            self.ax_3d.plot3D(*points.T, 'k-', alpha=0.7, linewidth=2)
        
        # 标记正面 (添加一个小的标记)
        front_center = R @ np.array([size*0.8, 0, 0])
        self.ax_3d.scatter(*front_center, color='red', s=50, alpha=0.8)
    
    def draw_data_monitor(self, new_data_count):
        """绘制数据监控信息"""
        self.ax_data.axis('off')

        # 计算欧拉角
        euler = self.quaternion_to_euler(self.current_quat)

        # 状态信息
        status_text = f"""
📡 串口: {'连接' if self.ser and self.ser.is_open else '断开'}
📊 数据: {len(self.quat_history)} 点
🔄 频率: {new_data_count} 点/帧
⏱️ 时间: {time.time() - self.start_time:.1f}s

📈 四元数:
   w: {self.current_quat[0]:+.3f}
   x: {self.current_quat[1]:+.3f}
   y: {self.current_quat[2]:+.3f}
   z: {self.current_quat[3]:+.3f}
   |q|: {np.linalg.norm(self.current_quat):.4f}

🎯 欧拉角:
   Roll:  {euler[0]:+6.1f}°
   Pitch: {euler[1]:+6.1f}°
   Yaw:   {euler[2]:+6.1f}°

⚙️ 标定状态:
   共轭: {'是' if self.quat_conjugate else '否'}
   X轴: {'反向' if self.axis_invert['x'] else '正向'}
   Y轴: {'反向' if self.axis_invert['y'] else '正向'}
   Z轴: {'反向' if self.axis_invert['z'] else '正向'}
        """

        self.ax_data.text(0.05, 0.95, status_text, transform=self.ax_data.transAxes,
                         fontsize=9, verticalalignment='top', fontfamily='monospace',
                         bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))

        self.ax_data.set_title('实时数据监控')

    def handle_keyboard_events(self):
        """处理键盘事件"""
        # 这里可以添加键盘事件处理
        # 由于matplotlib的限制，我们将在start()方法中设置键盘事件
        pass

    def on_key_press(self, event):
        """键盘按键事件处理"""
        if event.key == 'escape':
            self.running = False
            plt.close('all')
        elif event.key == ' ':  # 空格键重置
            self.reset_calibration()
        elif event.key == 'q':  # X轴反向
            self.axis_invert['x'] = not self.axis_invert['x']
            print(f"X轴反向: {self.axis_invert['x']}")
        elif event.key == 'w':  # Y轴反向
            self.axis_invert['y'] = not self.axis_invert['y']
            print(f"Y轴反向: {self.axis_invert['y']}")
        elif event.key == 'e':  # Z轴反向
            self.axis_invert['z'] = not self.axis_invert['z']
            print(f"Z轴反向: {self.axis_invert['z']}")
        elif event.key == 'r':  # 切换四元数顺序
            self.cycle_quaternion_order()
        elif event.key == 't':  # 共轭四元数
            self.quat_conjugate = not self.quat_conjugate
            print(f"四元数共轭: {self.quat_conjugate}")
        elif event.key == 's':  # 保存配置
            self.save_calibration_config()
        elif event.key == 'l':  # 加载配置
            self.load_calibration_config()
        elif event.key in '123456789':
            self.handle_axis_mapping(event.key)

    def reset_calibration(self):
        """重置标定参数"""
        self.axis_mapping = {'x': 0, 'y': 1, 'z': 2}
        self.axis_invert = {'x': False, 'y': False, 'z': False}
        self.quat_order = [0, 1, 2, 3]
        self.quat_conjugate = False
        print("标定参数已重置")

    def cycle_quaternion_order(self):
        """循环切换四元数顺序"""
        orders = [
            [0, 1, 2, 3],  # w,x,y,z
            [1, 2, 3, 0],  # x,y,z,w
            [3, 0, 1, 2],  # z,w,x,y
        ]
        current_idx = 0
        for i, order in enumerate(orders):
            if self.quat_order == order:
                current_idx = i
                break

        next_idx = (current_idx + 1) % len(orders)
        self.quat_order = orders[next_idx]
        order_names = ['w,x,y,z', 'x,y,z,w', 'z,w,x,y']
        print(f"四元数顺序: {order_names[next_idx]}")

    def handle_axis_mapping(self, key):
        """处理轴映射按键"""
        # 1,2,3 控制X轴映射到哪个轴
        # 4,5,6 控制Y轴映射到哪个轴
        # 7,8,9 控制Z轴映射到哪个轴
        key_map = {
            '1': ('x', 0), '2': ('x', 1), '3': ('x', 2),
            '4': ('y', 0), '5': ('y', 1), '6': ('y', 2),
            '7': ('z', 0), '8': ('z', 1), '9': ('z', 2),
        }

        if key in key_map:
            axis, target = key_map[key]
            self.axis_mapping[axis] = target
            axis_names = ['X', 'Y', 'Z']
            print(f"{axis.upper()}轴映射到: {axis_names[target]}轴")
    
    def start(self):
        """启动可视化系统"""
        print("🚀 启动SH5001优化3D可视化系统")
        print(f"📡 串口: {self.port}, 波特率: {self.baudrate}")
        print("📊 四元数格式: w,x,y,z (逗号分隔)")
        print("⚙️  坐标轴标定功能已启用")
        print("🎮 键盘控制:")
        print("   Q/W/E - 反转X/Y/Z轴")
        print("   R - 切换四元数顺序")
        print("   T - 使用共轭四元数")
        print("   SPACE - 重置标定")
        print("   ESC - 退出程序")
        print("⏹️  按Ctrl+C或关闭窗口退出")

        if not self.connect_serial():
            print("❌ 无法连接串口，请检查设备连接")
            return

        self.running = True
        self.start_time = time.time()

        # 设置键盘事件
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

        # 启动串口读取线程
        serial_thread = threading.Thread(target=self.read_serial_thread)
        serial_thread.daemon = True
        serial_thread.start()

        # 启动动画 - 使用优化的更新间隔
        self.animation = animation.FuncAnimation(
            self.fig, self.update_visualization,
            interval=self.update_interval, blit=False, cache_frame_data=False
        )

        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n⏹️  程序已停止")
        finally:
            self.running = False
            if self.ser:
                self.ser.close()

def test_mode():
    """测试模式 - 使用模拟数据"""
    print("🧪 测试模式 - 使用模拟数据")
    print("🎮 可以使用键盘测试标定功能")

    import math

    class TestVisualizer(SH5001Simple3D):
        def __init__(self):
            super().__init__()
            self.test_time = 0
            self.test_mode_type = 0  # 0: Z轴旋转, 1: 复合旋转, 2: 摆动

        def connect_serial(self):
            print("✓ 测试模式 - 跳过串口连接")
            return True

        def read_serial_thread(self):
            """模拟串口数据 - 多种运动模式"""
            while self.running:
                t = self.test_time * 0.02  # 时间因子

                if self.test_mode_type == 0:
                    # 绕Z轴旋转
                    angle = t
                    w = math.cos(angle / 2)
                    x = 0
                    y = 0
                    z = math.sin(angle / 2)
                elif self.test_mode_type == 1:
                    # 复合旋转
                    angle_z = t
                    angle_x = t * 0.3
                    # 简化的复合旋转
                    w = math.cos(angle_z/2) * math.cos(angle_x/2)
                    x = math.sin(angle_x/2) * math.cos(angle_z/2)
                    y = 0
                    z = math.sin(angle_z/2) * math.cos(angle_x/2)
                else:
                    # 摆动模式
                    angle = math.sin(t) * 0.5  # 摆动幅度
                    w = math.cos(angle / 2)
                    x = math.sin(angle / 2)
                    y = 0
                    z = 0

                # 添加少量噪声
                noise = 0.005
                w += (np.random.random() - 0.5) * noise
                x += (np.random.random() - 0.5) * noise
                y += (np.random.random() - 0.5) * noise
                z += (np.random.random() - 0.5) * noise

                quat = np.array([w, x, y, z])
                quat = quat / np.linalg.norm(quat)  # 归一化

                timestamp = time.time()
                if self.data_queue.qsize() < 5:  # 限制队列大小
                    self.data_queue.put((timestamp, quat))

                self.test_time += 1

                # 每5秒切换运动模式
                if self.test_time % 250 == 0:
                    self.test_mode_type = (self.test_mode_type + 1) % 3
                    modes = ['Z轴旋转', '复合旋转', '摆动模式']
                    print(f"切换到: {modes[self.test_mode_type]}")

                time.sleep(0.02)  # 50Hz

    visualizer = TestVisualizer()
    visualizer.start()

def main():
    """主函数"""
    print("🎯 SH5001传感器简化3D可视化程序")
    print("="*50)

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        test_mode()
        return

    try:
        visualizer = SH5001Simple3D(port='COM6', baudrate=115200)
        visualizer.start()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        print("💡 提示: 如果没有连接传感器，可以使用测试模式:")
        print("   python sh5001_simple_3d.py --test")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
